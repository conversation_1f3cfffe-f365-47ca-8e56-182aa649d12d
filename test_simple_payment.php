<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 SIMPLE PAYMENT GATEWAY TEST\n";
echo "==============================\n\n";

// Test using existing transaction
echo "1. Finding existing transaction...\n";

try {
    $transaction = \App\Models\Transaction::with('items')
        ->where('status', 'pending')
        ->where('payment_method', 'gateway')
        ->orderBy('created_at', 'desc')
        ->first();
        
    if (!$transaction) {
        echo "   ❌ No pending gateway transactions found\n";
        echo "   💡 Create a transaction through POS first\n";
        exit(1);
    }
    
    echo "   ✅ Found transaction: ID {$transaction->id}\n";
    echo "   📊 Amount: {$transaction->total_amount}\n";
    echo "   📊 Customer: {$transaction->customer_name}\n";
    echo "   📊 Items: " . $transaction->items->count() . "\n";
    
} catch (\Exception $e) {
    echo "   ❌ Error finding transaction: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Test 2: Test MidtransService directly
echo "2. Testing MidtransService...\n";

try {
    $midtransService = new \App\Services\MidtransService();
    
    echo "   🚀 Creating Snap token for transaction {$transaction->id}...\n";
    $result = $midtransService->createSnapToken($transaction);
    
    if ($result['success']) {
        echo "   ✅ SUCCESS! Snap token created\n";
        echo "   🎫 Token: " . substr($result['snap_token'], 0, 30) . "...\n";
        echo "   🌐 Redirect URL: " . $result['redirect_url'] . "\n";
        
        echo "\n   🎉 PAYMENT GATEWAY IS WORKING!\n";
        echo "   💡 You can now use the POS system to create payments\n";
        
    } else {
        echo "   ❌ FAILED to create Snap token\n";
        echo "   📄 Error: " . $result['message'] . "\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Exception in MidtransService: " . $e->getMessage() . "\n";
    echo "   📄 Trace: " . $e->getTraceAsString() . "\n";
}

echo "\n";

echo "🏁 TEST COMPLETED!\n\n";

echo "📋 WHAT TO DO NEXT:\n";
echo "===================\n";
echo "1. 🌐 Open: http://127.0.0.1:8000/pos\n";
echo "2. 🔐 Login to system\n";
echo "3. 🛒 Add products to cart\n";
echo "4. 👤 Fill customer name (required)\n";
echo "5. 💳 Click 'Bayar dengan Midtrans'\n";
echo "6. ✅ Should redirect to Midtrans payment page\n\n";

echo "🔍 IF STILL GETTING 'Undefined array key 10023':\n";
echo "=================================================\n";
echo "1. Clear browser cache (Ctrl+F5)\n";
echo "2. Clear localStorage in browser console:\n";
echo "   localStorage.clear()\n";
echo "3. Logout and login again\n";
echo "4. Try with different products\n";
echo "5. Check Laravel logs for detailed errors\n\n";

echo "💡 The error should now be fixed with enhanced validation!\n";
