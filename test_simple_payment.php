<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Services\MidtransService;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Models\User;

echo "🚀 Simple Payment Gateway Test\n\n";

try {
    // Test 1: Configuration
    echo "1. Testing Configuration...\n";
    echo "   Merchant ID: " . config('midtrans.merchant_id') . "\n";
    echo "   Environment: " . (config('midtrans.is_production') ? 'Production' : 'Sandbox') . "\n\n";

    // Test 2: Create simple transaction for testing
    echo "2. Creating Simple Test Transaction...\n";
    
    $user = User::first();
    $product = ProcessedInventory::where('current_stock', '>', 0)->first();
    
    if (!$user || !$product) {
        echo "   ❌ Missing user or product data\n";
        exit;
    }

    // Create transaction with unique invoice
    $transaction = new Transaction();
    $transaction->user_id = $user->id;
    $transaction->customer_name = 'Test Customer';
    $transaction->customer_phone = '081234567890';
    $transaction->subtotal = 50000;
    $transaction->tax = 0;
    $transaction->discount = 0;
    $transaction->total_amount = 50000;
    $transaction->amount_paid = 0;
    $transaction->change_amount = 0;
    $transaction->payment_method = 'gateway';
    $transaction->status = 'pending';
    $transaction->notes = 'Simple test - ' . time();
    $transaction->invoice_number = 'TEST-' . time(); // Manual invoice to avoid conflicts
    $transaction->save();

    // Create transaction item
    $item = new TransactionItem();
    $item->transaction_id = $transaction->id;
    $item->processed_inventory_id = $product->id;
    $item->product_name = $product->name;
    $item->price = 25000;
    $item->quantity = 2;
    $item->subtotal = 50000;
    $item->save();

    echo "   ✅ Transaction created: " . $transaction->invoice_number . "\n\n";

    // Test 3: Create Snap Token
    echo "3. Testing Snap Token...\n";
    
    $midtransService = new MidtransService();
    $result = $midtransService->createSnapToken($transaction);

    if ($result['success']) {
        echo "   ✅ Snap token created successfully!\n";
        echo "   🔗 Payment URL: " . $result['redirect_url'] . "\n\n";
        
        echo "4. Test Information:\n";
        echo "   Transaction ID: " . $transaction->id . "\n";
        echo "   Invoice: " . $transaction->invoice_number . "\n";
        echo "   Amount: Rp " . number_format($transaction->total_amount, 0, ',', '.') . "\n";
        echo "   Payment URL: " . $result['redirect_url'] . "\n\n";
        
        echo "5. Test Cards:\n";
        echo "   ✅ Success: 4811 1111 1111 1114 (CVV: 123, Exp: 01/25)\n";
        echo "   ❌ Failed:  4911 1111 1111 1113 (CVV: 123, Exp: 01/25)\n\n";
        
        echo "✅ Payment Gateway Test SUCCESSFUL!\n";
        echo "Open the payment URL to test the payment flow.\n";
        
    } else {
        echo "   ❌ Failed: " . $result['message'] . "\n";
        
        // Clean up
        $transaction->items()->delete();
        $transaction->delete();
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    
    // Clean up if needed
    if (isset($transaction)) {
        $transaction->items()->delete();
        $transaction->delete();
    }
}
