<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-seedling"></i>
        <span>Stok Ubi Mentah</span>
    </div>

    <?php if($lowStock->count() > 0): ?>
    <div class="alert alert-warning mb-4">
        <h5><i class="fas fa-exclamation-triangle"></i> Peringatan Stok Menipis</h5>
        <p class="mb-0">
            Beberapa item stok ubi mentah sudah hampir habis:
            <ul class="mb-0">
                <?php $__currentLoopData = $lowStock; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li><?php echo e($item->name); ?> (<?php echo e(number_format($item->current_stock, 2, ',', '.')); ?> kg)</li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </p>
    </div>
    <?php endif; ?>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Daftar Stok Ubi Mentah</span>
                    <a href="<?php echo e(route('raw-inventory.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Stok Baru
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>No.</th>
                                    <th>Nama Ubi</th>
                                    <th>Batch</th>
                                    <th>Supplier</th>
                                    <th>Kualitas</th>
                                    <th>Stok (kg)</th>
                                    <th>Harga/kg</th>
                                    <th>Total Nilai</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $rawInventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($loop->iteration); ?></td>
                                    <td><?php echo e($item->name); ?></td>
                                    <td><?php echo e($item->batch_number); ?></td>
                                    <td><?php echo e($item->supplier_name ?: '-'); ?></td>
                                    <td>
                                        <?php if($item->quality == 'A'): ?>
                                            <span class="badge bg-success">A</span>
                                        <?php elseif($item->quality == 'B'): ?>
                                            <span class="badge bg-info">B</span>
                                        <?php else: ?>
                                            <span class="badge bg-warning">C</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e(number_format($item->current_stock, 2, ',', '.')); ?>

                                        <?php if($item->current_stock <= $item->min_stock_threshold): ?>
                                            <span class="badge bg-danger ms-1">!</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>Rp <?php echo e(number_format($item->cost_per_kg, 0, ',', '.')); ?></td>
                                    <td>Rp <?php echo e(number_format($item->total_cost, 0, ',', '.')); ?></td>
                                    <td>
                                        <?php if($item->is_active): ?>
                                            <span class="badge bg-success">Aktif</span>
                                        <?php else: ?>
                                            <span class="badge bg-danger">Tidak Aktif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="<?php echo e(route('raw-inventory.show', $item)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="<?php echo e(route('raw-inventory.edit', $item)); ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#addStockModal<?php echo e($item->id); ?>">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" onclick="confirmDelete('<?php echo e($item->id); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>

                                        <!-- Modal Tambah Stok -->
                                        <div class="modal fade" id="addStockModal<?php echo e($item->id); ?>" tabindex="-1" aria-labelledby="addStockModalLabel<?php echo e($item->id); ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="addStockModalLabel<?php echo e($item->id); ?>">Tambah Stok - <?php echo e($item->name); ?></h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <form action="<?php echo e(route('raw-inventory.add-stock', $item)); ?>" method="POST">
                                                        <?php echo csrf_field(); ?>
                                                        <div class="modal-body">
                                                            <div class="mb-3">
                                                                <label for="additional_stock" class="form-label">Jumlah Tambahan (kg) <span class="text-danger">*</span></label>
                                                                <input type="number" class="form-control" id="additional_stock" name="additional_stock" step="0.1" min="0.1" required>
                                                            </div>
                                                            <div class="mb-3">
                                                                <label for="cost_per_kg" class="form-label">Harga per kg (Rp) <span class="text-danger">*</span></label>
                                                                <input type="number" class="form-control" id="cost_per_kg" name="cost_per_kg" value="<?php echo e($item->cost_per_kg); ?>" min="0" required>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                            <button type="submit" class="btn btn-success">Tambah Stok</button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Form Delete -->
                                        <form id="delete-form-<?php echo e($item->id); ?>" action="<?php echo e(route('raw-inventory.destroy', $item)); ?>" method="POST" class="d-none">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="10" class="text-center">Tidak ada data stok ubi mentah.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function confirmDelete(id) {
    if (confirm('Apakah Anda yakin ingin menghapus data ini?')) {
        document.getElementById('delete-form-' + id).submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\WEB\iterasi 2\ubi-bakar-cilembu\resources\views/inventory/raw/index.blade.php ENDPATH**/ ?>