@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-receipt"></i>
        <span>Detail Transaksi</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail Transaksi #{{ $transaction->invoice_number }}</span>
                    <div>
                        @if($transaction->status === 'pending' && $transaction->payment_gateway)
                            <button class="btn btn-warning me-2" onclick="checkPaymentStatus({{ $transaction->id }})">
                                <i class="fas fa-sync"></i> Cek Status
                            </button>
                            @if($transaction->snap_token)
                                <a href="{{ config('midtrans.snap_url') }}{{ $transaction->snap_token }}"
                                   class="btn btn-success me-2" target="_blank">
                                    <i class="fas fa-credit-card"></i> <PERSON>ar <PERSON>
                                </a>
                            @endif
                            <button class="btn btn-danger me-2" onclick="cancelPayment({{ $transaction->id }})">
                                <i class="fas fa-times"></i> Batalkan
                            </button>
                            <!-- Manual Update Status for Testing -->
                            @if(auth()->user()->role === 'admin')
                            <div class="dropdown d-inline-block me-2">
                                <button class="btn btn-warning dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-edit"></i> Update Status
                                </button>
                                <ul class="dropdown-menu">
                                    <li>
                                        <form action="{{ route('payment.update-status', $transaction) }}" method="POST" class="d-inline">
                                            @csrf
                                            <input type="hidden" name="status" value="completed">
                                            <input type="hidden" name="payment_status" value="paid">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-check text-success"></i> Mark as Paid
                                            </button>
                                        </form>
                                    </li>
                                    <li>
                                        <form action="{{ route('payment.update-status', $transaction) }}" method="POST" class="d-inline">
                                            @csrf
                                            <input type="hidden" name="status" value="failed">
                                            <input type="hidden" name="payment_status" value="failed">
                                            <button type="submit" class="dropdown-item">
                                                <i class="fas fa-times text-danger"></i> Mark as Failed
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                            @endif
                        @endif
                        @if($transaction->status === 'completed')
                            <a href="{{ route('transactions.print', $transaction) }}" class="btn btn-primary" target="_blank">
                                <i class="fas fa-print"></i> Cetak
                            </a>
                        @endif
                        <a href="{{ route('transactions.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Informasi Transaksi</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%">No. Invoice</th>
                                    <td>{{ $transaction->invoice_number }}</td>
                                </tr>
                                <tr>
                                    <th>Tanggal & Waktu</th>
                                    <td>{{ $transaction->created_at->format('d M Y H:i:s') }}</td>
                                </tr>
                                <tr>
                                    <th>Kasir</th>
                                    <td>{{ $transaction->user->name ?? 'Admin' }}</td>
                                </tr>
                                <tr>
                                    <th>Catatan</th>
                                    <td>{{ $transaction->note ?? '-' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Informasi Pembayaran</h5>
                            <table class="table table-bordered">
                                <tr>
                                    <th style="width: 40%">Total</th>
                                    <td>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
                                </tr>
                                <tr>
                                    <th>Status</th>
                                    <td>{!! $transaction->status_badge !!}</td>
                                </tr>
                                <tr>
                                    <th>Metode Pembayaran</th>
                                    <td>{{ $transaction->payment_gateway_display }}</td>
                                </tr>
                                @if($transaction->payment_gateway)
                                <tr>
                                    <th>Payment Gateway</th>
                                    <td>{{ ucfirst($transaction->payment_gateway) }}</td>
                                </tr>
                                @if($transaction->payment_gateway_status)
                                <tr>
                                    <th>Status Gateway</th>
                                    <td>
                                        <span class="badge bg-{{ $transaction->payment_gateway_status === 'settlement' ? 'success' : ($transaction->payment_gateway_status === 'pending' ? 'warning' : 'secondary') }}">
                                            {{ ucfirst($transaction->payment_gateway_status) }}
                                        </span>
                                    </td>
                                </tr>
                                @endif
                                @if($transaction->payment_gateway_paid_at)
                                <tr>
                                    <th>Dibayar Pada</th>
                                    <td>{{ $transaction->payment_gateway_paid_at->format('d M Y H:i:s') }}</td>
                                </tr>
                                @endif
                                @endif
                                <tr>
                                    <th>Jumlah Dibayar</th>
                                    <td>Rp {{ number_format($transaction->amount_paid, 0, ',', '.') }}</td>
                                </tr>
                                @if($transaction->change_amount > 0)
                                <tr>
                                    <th>Kembalian</th>
                                    <td>Rp {{ number_format($transaction->change_amount, 0, ',', '.') }}</td>
                                </tr>
                                @endif
                            </table>
                        </div>
                    </div>

                    <h5>Daftar Item</h5>
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Harga Satuan</th>
                                    <th>Jumlah</th>
                                    <th>Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($transaction->items as $index => $item)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $item->product_name }}</td>
                                    <td>Rp {{ number_format($item->price, 0, ',', '.') }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>Rp {{ number_format($item->subtotal, 0, ',', '.') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="4" class="text-end">Total</th>
                                    <th>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    function checkPaymentStatus(transactionId, isAutoCheck = false) {
        // Jika ini adalah auto-check, jangan tampilkan loading di button
        const button = isAutoCheck ? null : event.target;
        const originalText = button ? button.innerHTML : '';

        if (button) {
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Mengecek...';
        }

        fetch(`/payment/status/${transactionId}`, {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }).catch(() => {
                    throw new Error(`HTTP error! status: ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            console.log('Payment status response:', data);

            if (data.success) {
                if (data.transaction_status === 'completed') {
                    if (!isAutoCheck) {
                        alert('Pembayaran berhasil! Halaman akan dimuat ulang.');
                    }
                    location.reload();
                } else {
                    if (!isAutoCheck) {
                        alert(data.message || `Status pembayaran: ${data.payment_status || 'pending'}`);
                    }
                    console.log('Payment status check:', data.payment_status);
                }
            } else {
                if (!isAutoCheck) {
                    let errorMessage = data.message || 'Gagal memeriksa status pembayaran';
                    if (data.error_detail) {
                        console.error('Error detail:', data.error_detail);
                        // Tampilkan detail error jika diperlukan untuk debugging
                        errorMessage += '\n\nDetail: ' + data.error_detail;
                    }
                    alert(errorMessage);
                }
                console.error('Payment status check failed:', data);
            }
        })
        .catch(error => {
            console.error('Error checking payment status:', error);
            if (!isAutoCheck) {
                alert('Terjadi kesalahan saat memeriksa status pembayaran: ' + error.message);
            }
        })
        .finally(() => {
            if (button) {
                button.disabled = false;
                button.innerHTML = originalText;
            }
        });
    }

    function cancelPayment(transactionId) {
        if (!confirm('Apakah Anda yakin ingin membatalkan pembayaran ini?')) {
            return;
        }

        const button = event.target;
        const originalText = button.innerHTML;

        button.disabled = true;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Membatalkan...';

        fetch(`/payment/cancel/${transactionId}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Pembayaran berhasil dibatalkan. Halaman akan dimuat ulang.');
                location.reload();
            } else {
                alert('Gagal membatalkan pembayaran: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Terjadi kesalahan saat membatalkan pembayaran');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    // Auto-refresh status for pending transactions - DISABLED TEMPORARILY
    @if(false && $transaction->status === 'pending' && $transaction->payment_gateway)
    let autoCheckInterval;
    let autoCheckCount = 0;
    const maxAutoChecks = 5; // Reduced to 5 checks maximum

    // Only start auto-check if we're actually on the transaction show page
    const currentPath = window.location.pathname;
    const expectedPath = '/transactions/{{ $transaction->id }}';

    if (currentPath.includes(expectedPath)) {
        console.log('Starting auto-check for transaction {{ $transaction->id }}');

        function startAutoCheck() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
            }

            autoCheckInterval = setInterval(function() {
                // Multiple stop conditions
                if (autoCheckCount >= maxAutoChecks ||
                    document.hidden ||
                    !window.location.pathname.includes(expectedPath)) {
                    clearInterval(autoCheckInterval);
                    console.log('Auto-check stopped - conditions met');
                    return;
                }

                autoCheckCount++;
                console.log(`Auto-checking payment status (${autoCheckCount}/${maxAutoChecks})`);
                checkPaymentStatus({{ $transaction->id }}, true);
            }, 60000); // Increased to 60 seconds
        }

        // Start auto-check with delay
        setTimeout(startAutoCheck, 5000);

        // Stop auto-check when page becomes hidden
        document.addEventListener('visibilitychange', function() {
            if (document.hidden && autoCheckInterval) {
                clearInterval(autoCheckInterval);
                console.log('Auto-check paused - page hidden');
            }
        });

        // Stop auto-check when user navigates away
        window.addEventListener('beforeunload', function() {
            if (autoCheckInterval) {
                clearInterval(autoCheckInterval);
                console.log('Auto-check stopped - page unload');
            }
        });
    } else {
        console.log('Not on transaction page, skipping auto-check');
    }
    @endif
</script>
@endpush

@endsection