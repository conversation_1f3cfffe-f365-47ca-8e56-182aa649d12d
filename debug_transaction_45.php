<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 DEBUGGING TRANSACTION 45\n";
echo "===========================\n\n";

try {
    $transaction = \App\Models\Transaction::with('items')->find(45);
    
    if (!$transaction) {
        echo "❌ Transaction 45 not found\n";
        exit(1);
    }
    
    echo "✅ Transaction 45 found\n";
    echo "📊 Details:\n";
    echo "   ID: {$transaction->id}\n";
    echo "   Invoice: {$transaction->invoice_number}\n";
    echo "   Customer: {$transaction->customer_name}\n";
    echo "   Total: {$transaction->total_amount}\n";
    echo "   Status: {$transaction->status}\n";
    echo "   Payment Method: {$transaction->payment_method}\n";
    echo "   Items Count: " . $transaction->items->count() . "\n\n";
    
    echo "📋 Transaction Items:\n";
    foreach ($transaction->items as $index => $item) {
        echo "   Item #{$index}:\n";
        echo "      ID: {$item->id}\n";
        echo "      Product Name: '{$item->product_name}'\n";
        echo "      Price: {$item->price}\n";
        echo "      Quantity: {$item->quantity}\n";
        echo "      Subtotal: {$item->subtotal}\n";
        echo "      Processed Inventory ID: " . ($item->processed_inventory_id ?: 'NULL') . "\n";
        echo "      Other Product ID: " . ($item->product_id ?: 'NULL') . "\n";
        
        // Check if products exist
        if ($item->processed_inventory_id) {
            $processed = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
            if ($processed) {
                echo "      ✅ ProcessedInventory exists: {$processed->name}\n";
            } else {
                echo "      ❌ ProcessedInventory NOT FOUND: {$item->processed_inventory_id}\n";
            }
        }
        
        if ($item->product_id) {
            $other = \App\Models\OtherProduct::find($item->product_id);
            if ($other) {
                echo "      ✅ OtherProduct exists: {$other->name}\n";
            } else {
                echo "      ❌ OtherProduct NOT FOUND: {$item->product_id}\n";
            }
        }
        echo "\n";
    }
    
    // Now let's trace through the MidtransService step by step
    echo "🔍 TRACING MIDTRANS SERVICE:\n";
    echo "============================\n";
    
    // Step 1: Build transaction params manually
    echo "1. Building transaction params...\n";
    
    $orderId = 'UBI-' . $transaction->id . '-' . time();
    echo "   Order ID: {$orderId}\n";
    
    $itemDetails = [];
    foreach ($transaction->items as $index => $item) {
        echo "   Processing item #{$index}:\n";
        echo "      Item ID: {$item->id}\n";
        echo "      Product Name: '{$item->product_name}'\n";
        echo "      Price: {$item->price}\n";
        echo "      Quantity: {$item->quantity}\n";
        
        // Check for any issues with item data
        if (!$item->product_name) {
            echo "      ❌ ISSUE: Product name is empty\n";
        }
        if (!$item->price || $item->price <= 0) {
            echo "      ❌ ISSUE: Invalid price: {$item->price}\n";
        }
        if (!$item->quantity || $item->quantity <= 0) {
            echo "      ❌ ISSUE: Invalid quantity: {$item->quantity}\n";
        }
        
        $itemDetails[] = [
            'id' => 'ITEM-' . $item->id,
            'price' => (int) $item->price,
            'quantity' => (int) $item->quantity,
            'name' => substr($item->product_name, 0, 50),
            'category' => 'Food'
        ];
        
        echo "      ✅ Item processed for Midtrans\n";
    }
    
    echo "\n   📊 Item Details Array:\n";
    foreach ($itemDetails as $index => $detail) {
        echo "      [{$index}] ID: {$detail['id']}, Name: '{$detail['name']}', Price: {$detail['price']}, Qty: {$detail['quantity']}\n";
    }
    
    // Step 2: Build full params
    echo "\n2. Building full transaction params...\n";
    
    $params = [
        'transaction_details' => [
            'order_id' => $orderId,
            'gross_amount' => (int) $transaction->total_amount,
        ],
        'item_details' => $itemDetails,
        'customer_details' => [
            'first_name' => $transaction->customer_name ?: 'Customer',
            'phone' => $transaction->customer_phone ?: '081234567890'
        ],
        'enabled_payments' => [
            'credit_card', 'bca_va', 'bni_va', 'bri_va',
            'echannel', 'permata_va', 'other_va', 'gopay',
            'shopeepay', 'qris'
        ],
        'callbacks' => [
            'finish' => url('/payment/finish'),
            'unfinish' => url('/payment/unfinish'),
            'error' => url('/payment/error')
        ]
    ];
    
    echo "   ✅ Transaction params built successfully\n";
    echo "   📊 Order ID: {$params['transaction_details']['order_id']}\n";
    echo "   📊 Gross Amount: {$params['transaction_details']['gross_amount']}\n";
    echo "   📊 Items Count: " . count($params['item_details']) . "\n";
    echo "   📊 Customer: {$params['customer_details']['first_name']}\n";
    
    // Step 3: Test Midtrans configuration
    echo "\n3. Testing Midtrans configuration...\n";
    
    \Midtrans\Config::$serverKey = config('midtrans.server_key');
    \Midtrans\Config::$isProduction = config('midtrans.is_production');
    \Midtrans\Config::$isSanitized = config('midtrans.is_sanitized');
    \Midtrans\Config::$is3ds = config('midtrans.is_3ds');
    
    echo "   📊 Server Key: " . (strlen(\Midtrans\Config::$serverKey) > 0 ? 'SET (' . strlen(\Midtrans\Config::$serverKey) . ' chars)' : 'NOT SET') . "\n";
    echo "   📊 Is Production: " . (\Midtrans\Config::$isProduction ? 'true' : 'false') . "\n";
    
    // Step 4: Try to create Snap token
    echo "\n4. Creating Snap token...\n";
    
    try {
        $snapToken = \Midtrans\Snap::getSnapToken($params);
        echo "   ✅ SUCCESS! Snap token created: " . substr($snapToken, 0, 30) . "...\n";
        
        $redirectUrl = 'https://app.sandbox.midtrans.com/snap/v2/vtweb/' . $snapToken;
        echo "   🌐 Redirect URL: {$redirectUrl}\n";
        
    } catch (\Exception $e) {
        echo "   ❌ FAILED to create Snap token\n";
        echo "   📄 Error: " . $e->getMessage() . "\n";
        echo "   📄 Error Class: " . get_class($e) . "\n";
        echo "   📄 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        
        // Print stack trace to find where exactly the error occurs
        echo "\n   📄 Stack Trace:\n";
        $trace = $e->getTrace();
        foreach ($trace as $index => $step) {
            if (isset($step['file']) && isset($step['line'])) {
                echo "      #{$index} {$step['file']}:{$step['line']}\n";
                if (isset($step['function'])) {
                    echo "         Function: {$step['function']}\n";
                }
            }
        }
    }
    
} catch (\Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "📄 File: " . $e->getFile() . ":" . $e->getLine() . "\n";
}

echo "\n🏁 Debug completed!\n";
