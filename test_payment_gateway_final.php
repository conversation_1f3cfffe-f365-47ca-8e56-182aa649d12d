<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 FINAL PAYMENT GATEWAY TEST\n";
echo "=============================\n\n";

// Test 1: Create a test transaction
echo "1. Creating test transaction...\n";

try {
    // Get a valid product
    $product = \App\Models\ProcessedInventory::where('is_active', true)->first();
    
    if (!$product) {
        echo "   ❌ No active products found\n";
        exit(1);
    }
    
    echo "   ✅ Using product: {$product->name} (ID: {$product->id})\n";
    
    // Create transaction
    $transaction = \App\Models\Transaction::create([
        'invoice_number' => 'TEST-' . time(),
        'customer_name' => 'Test Customer',
        'customer_phone' => '081234567890',
        'total_amount' => $product->selling_price * 2,
        'payment_method' => 'gateway',
        'status' => 'pending',
        'notes' => 'Test transaction for payment gateway',
        'created_by' => 1
    ]);
    
    echo "   ✅ Transaction created: ID {$transaction->id}\n";
    
    // Create transaction item
    $transactionItem = \App\Models\TransactionItem::create([
        'transaction_id' => $transaction->id,
        'processed_inventory_id' => $product->id,
        'product_name' => $product->name,
        'price' => $product->selling_price,
        'quantity' => 2,
        'subtotal' => $product->selling_price * 2
    ]);
    
    echo "   ✅ Transaction item created: ID {$transactionItem->id}\n";
    
} catch (\Exception $e) {
    echo "   ❌ Error creating transaction: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n";

// Test 2: Test MidtransService
echo "2. Testing MidtransService...\n";

try {
    $midtransService = new \App\Services\MidtransService();
    
    echo "   🚀 Creating Snap token...\n";
    $result = $midtransService->createSnapToken($transaction);
    
    if ($result['success']) {
        echo "   ✅ Snap token created successfully!\n";
        echo "   🎫 Token: " . substr($result['snap_token'], 0, 30) . "...\n";
        echo "   🌐 Redirect URL: " . $result['redirect_url'] . "\n";
        
        // Test the redirect URL
        echo "   🔍 Testing redirect URL...\n";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $result['redirect_url']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            echo "   ✅ Redirect URL is accessible (HTTP {$httpCode})\n";
        } else {
            echo "   ⚠️  Redirect URL returned HTTP {$httpCode}\n";
        }
        
    } else {
        echo "   ❌ Failed to create Snap token: " . $result['message'] . "\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error testing MidtransService: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test PaymentController endpoint
echo "3. Testing PaymentController endpoint...\n";

try {
    // Simulate HTTP request to payment creation endpoint
    $url = "http://127.0.0.1:8000/payment/create/{$transaction->id}";
    
    echo "   🌐 Testing URL: {$url}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // Don't follow redirects
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'User-Agent: Mozilla/5.0 (Test Bot)'
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $redirectUrl = curl_getinfo($ch, CURLINFO_REDIRECT_URL);
    curl_close($ch);
    
    echo "   📊 HTTP Code: {$httpCode}\n";
    
    if ($httpCode == 302 && $redirectUrl) {
        echo "   ✅ Redirect successful to: " . substr($redirectUrl, 0, 80) . "...\n";
        
        if (strpos($redirectUrl, 'midtrans.com') !== false) {
            echo "   ✅ Redirected to Midtrans payment page\n";
        } else {
            echo "   ⚠️  Redirected to: {$redirectUrl}\n";
        }
    } elseif ($httpCode == 200) {
        echo "   ⚠️  Got 200 response instead of redirect\n";
        echo "   📄 Response: " . substr($response, 0, 200) . "...\n";
    } else {
        echo "   ❌ Unexpected response: HTTP {$httpCode}\n";
        echo "   📄 Response: " . substr($response, 0, 200) . "...\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error testing endpoint: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Cleanup
echo "4. Cleaning up test data...\n";

try {
    $transactionItem->delete();
    $transaction->delete();
    echo "   ✅ Test transaction cleaned up\n";
} catch (\Exception $e) {
    echo "   ⚠️  Error cleaning up: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🏁 FINAL TEST COMPLETED!\n\n";

echo "📋 SUMMARY:\n";
echo "===========\n";
echo "✅ MidtransService: Enhanced with better error handling\n";
echo "✅ Transaction validation: Added comprehensive checks\n";
echo "✅ Item processing: Added validation and logging\n";
echo "✅ Configuration: Midtrans config properly loaded\n";
echo "✅ Error handling: Improved error messages and logging\n\n";

echo "🎯 NEXT STEPS:\n";
echo "==============\n";
echo "1. 🌐 Open: http://127.0.0.1:8000/pos\n";
echo "2. 🔐 Login to system\n";
echo "3. 🛒 Add products to cart\n";
echo "4. 👤 Fill customer name\n";
echo "5. 💳 Click 'Bayar dengan Midtrans'\n";
echo "6. ✅ Should redirect directly to Midtrans\n\n";

echo "🔧 IF STILL GETTING ERRORS:\n";
echo "===========================\n";
echo "1. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "2. Clear browser cache and localStorage\n";
echo "3. Ensure you're logged in to the system\n";
echo "4. Try with different products\n";
echo "5. Check network connectivity\n\n";

echo "💡 The 'Undefined array key 10023' error should now be resolved!\n";
