

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-receipt"></i>
        <span>Struk Transaksi #<?php echo e($transaction->invoice_number); ?></span>
    </div>

    <div class="row mb-4">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Struk Transaksi</span>
                    <div>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> Cetak
                        </button>
                        <a href="<?php echo e(route('transactions.show', $transaction)); ?>" class="btn btn-secondary">
                            <i class="fas fa-eye"></i> Lihat Detail
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="receipt-container" style="font-family: 'Courier New', monospace; max-width: 400px; margin: 0 auto; padding: 20px; border: 1px dashed #ccc;">
                        <div class="text-center mb-4">
                            <h3 style="margin: 0;">UBI BAKAR CILEMBU</h3>
                            <p style="margin: 0;">Jl. Contoh No. 123, Kota, Indonesia</p>
                            <p style="margin: 0;">Telp: 081234567890</p>
                        </div>
                        
                        <hr style="border-top: 1px dashed #000;">
                        
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-4">No. Invoice</div>
                                <div class="col-1">:</div>
                                <div class="col-7"><?php echo e($transaction->invoice_number); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-4">Tanggal</div>
                                <div class="col-1">:</div>
                                <div class="col-7"><?php echo e($transaction->created_at->format('d/m/Y H:i')); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-4">Kasir</div>
                                <div class="col-1">:</div>
                                <div class="col-7"><?php echo e($transaction->user->name ?? 'Admin'); ?></div>
                            </div>
                        </div>
                        
                        <hr style="border-top: 1px dashed #000;">
                        
                        <table class="table table-sm" style="font-size: 14px;">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th class="text-center">Qty</th>
                                    <th class="text-end">Harga</th>
                                    <th class="text-end">Subtotal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $transaction->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($item->product_name); ?></td>
                                    <td class="text-center"><?php echo e($item->quantity); ?></td>
                                    <td class="text-end"><?php echo e(number_format($item->price, 0, ',', '.')); ?></td>
                                    <td class="text-end"><?php echo e(number_format($item->subtotal, 0, ',', '.')); ?></td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                        </table>
                        
                        <hr style="border-top: 1px dashed #000;">
                        
                        <div style="font-size: 14px;">
                            <div class="row">
                                <div class="col-6">Total</div>
                                <div class="col-6 text-end fw-bold">Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-6">Bayar (<?php echo e($transaction->payment_method); ?>)</div>
                                <div class="col-6 text-end">Rp <?php echo e(number_format($transaction->amount_paid, 0, ',', '.')); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-6">Kembali</div>
                                <div class="col-6 text-end">Rp <?php echo e(number_format($transaction->change_amount, 0, ',', '.')); ?></div>
                            </div>
                        </div>
                        
                        <hr style="border-top: 1px dashed #000;">
                        
                        <div class="text-center mt-4">
                            <p style="margin: 0;">Terima Kasih Atas Kunjungan Anda</p>
                            <p style="margin: 0;">Selamat Menikmati!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style type="text/css" media="print">
    @page {
        size: 80mm auto;
        margin: 0;
    }
    body {
        margin: 0;
        padding: 0;
    }
    .container-fluid, .page-title, .card-header, footer {
        display: none;
    }
    .card {
        border: none !important;
    }
    .card-body {
        padding: 0 !important;
    }
    .receipt-container {
        border: none !important;
        padding: 0 !important;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\WEB\iterasi 2\ubi-bakar-cilembu\resources\views/transactions/receipt.blade.php ENDPATH**/ ?>