<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\MidtransService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PaymentGatewayController extends Controller
{
    protected $midtransService;

    public function __construct(MidtransService $midtransService)
    {
        $this->midtransService = $midtransService;
    }

    /**
     * Create payment via Midtrans
     */
    public function createPayment(Request $request, Transaction $transaction)
    {
        try {
            Log::info('Creating payment for transaction', [
                'transaction_id' => $transaction->id,
                'status' => $transaction->status,
                'total_amount' => $transaction->total_amount,
                'items_count' => $transaction->items->count()
            ]);

            // Validate transaction
            if ($transaction->status !== 'pending') {
                Log::warning('Transaction status invalid', [
                    'transaction_id' => $transaction->id,
                    'current_status' => $transaction->status
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Transaksi tidak dapat diproses. Status: ' . $transaction->status
                ], 400);
            }

            // Validate transaction has items
            if ($transaction->items->count() === 0) {
                Log::warning('Transaction has no items', [
                    'transaction_id' => $transaction->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Transaksi tidak memiliki item'
                ], 400);
            }

            // Create Snap token
            Log::info('Creating Midtrans snap token', [
                'transaction_id' => $transaction->id
            ]);

            $result = $this->midtransService->createSnapToken($transaction);

            if (!$result['success']) {
                Log::error('Failed to create snap token', [
                    'transaction_id' => $transaction->id,
                    'result' => $result
                ]);

                return response()->json($result, 400);
            }

            Log::info('Payment gateway created successfully', [
                'transaction_id' => $transaction->id,
                'snap_token' => substr($result['snap_token'], 0, 20) . '...',
                'redirect_url' => $result['redirect_url']
            ]);

            return response()->json([
                'success' => true,
                'snap_token' => $result['snap_token'],
                'redirect_url' => $result['redirect_url'],
                'transaction_id' => $transaction->id
            ]);

        } catch (\Exception $e) {
            Log::error('Payment creation failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal membuat pembayaran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle Midtrans notification webhook
     */
    public function handleNotification(Request $request)
    {
        try {
            $notification = $request->all();
            
            Log::info('Midtrans notification received', $notification);

            $result = $this->midtransService->handleNotification($notification);

            if ($result['success']) {
                return response('OK', 200);
            } else {
                return response('FAILED', 400);
            }

        } catch (\Exception $e) {
            Log::error('Notification handling failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response('ERROR', 500);
        }
    }

    /**
     * Payment finish page
     */
    public function paymentFinish(Request $request)
    {
        $orderId = $request->get('order_id');
        $statusCode = $request->get('status_code');
        $transactionStatus = $request->get('transaction_status');

        Log::info('Payment finish callback', [
            'order_id' => $orderId,
            'status_code' => $statusCode,
            'transaction_status' => $transactionStatus
        ]);

        // Find transaction
        $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

        if (!$transaction) {
            return redirect()->route('transactions.index')
                           ->with('error', 'Transaksi tidak ditemukan');
        }

        // Check if payment is successful
        if (in_array($transactionStatus, ['capture', 'settlement'])) {
            return redirect()->route('transactions.show', $transaction)
                           ->with('success', 'Pembayaran berhasil! Transaksi telah dikonfirmasi.');
        } else {
            return redirect()->route('transactions.show', $transaction)
                           ->with('warning', 'Pembayaran sedang diproses. Status akan diperbarui secara otomatis.');
        }
    }

    /**
     * Payment unfinish page
     */
    public function paymentUnfinish(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::info('Payment unfinish callback', ['order_id' => $orderId]);

        $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

        if ($transaction) {
            return redirect()->route('transactions.show', $transaction)
                           ->with('warning', 'Pembayaran belum selesai. Silakan lanjutkan pembayaran atau pilih metode lain.');
        }

        return redirect()->route('transactions.index')
                       ->with('warning', 'Pembayaran belum selesai');
    }

    /**
     * Payment error page
     */
    public function paymentError(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::error('Payment error callback', [
            'order_id' => $orderId,
            'request' => $request->all()
        ]);

        $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

        if ($transaction) {
            return redirect()->route('transactions.show', $transaction)
                           ->with('error', 'Terjadi kesalahan dalam pembayaran. Silakan coba lagi atau pilih metode pembayaran lain.');
        }

        return redirect()->route('transactions.index')
                       ->with('error', 'Terjadi kesalahan dalam pembayaran');
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Transaction $transaction)
    {
        try {
            // Validate transaction exists and has payment gateway
            if (!$transaction || !$transaction->payment_gateway) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaksi tidak menggunakan payment gateway'
                ], 400);
            }

            if (empty($transaction->payment_gateway_order_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order ID tidak ditemukan'
                ], 400);
            }

            // If transaction is already completed, return current status
            if ($transaction->status === 'completed') {
                return response()->json([
                    'success' => true,
                    'transaction_status' => $transaction->status,
                    'payment_status' => $transaction->payment_gateway_status,
                    'message' => 'Transaksi sudah selesai'
                ]);
            }

            $result = $this->midtransService->checkTransactionStatus($transaction->payment_gateway_order_id);

            if ($result['success']) {
                // Update transaction status based on current status
                $status = $result['data'];
                $this->midtransService->handleNotification((array) $status);

                // Reload transaction to get updated status
                $transaction->refresh();

                return response()->json([
                    'success' => true,
                    'transaction_status' => $transaction->status,
                    'payment_status' => $transaction->payment_gateway_status,
                    'data' => $status
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Gagal memeriksa status pembayaran'
            ], 400);

        } catch (\Exception $e) {
            Log::error('Status check failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal memeriksa status pembayaran: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel payment
     */
    public function cancelPayment(Transaction $transaction)
    {
        try {
            DB::beginTransaction();

            // Return items to inventory if transaction was pending
            if ($transaction->status === 'pending') {
                foreach ($transaction->items as $item) {
                    if ($item->processed_inventory_id) {
                        $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                        if ($product) {
                            $product->increment('current_stock', $item->quantity);
                        }
                    } else {
                        $product = \App\Models\OtherProduct::find($item->product_id);
                        if ($product) {
                            $product->increment('current_stock', $item->quantity);
                        }
                    }
                }
            }

            // Update transaction status
            $transaction->update([
                'status' => 'cancelled',
                'payment_gateway_status' => 'cancelled'
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Pembayaran berhasil dibatalkan'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Payment cancellation failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal membatalkan pembayaran'
            ], 500);
        }
    }
}
