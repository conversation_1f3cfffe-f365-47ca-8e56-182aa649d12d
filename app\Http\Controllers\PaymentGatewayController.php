<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\MidtransService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class PaymentGatewayController extends Controller
{
    protected $midtransService;

    public function __construct(MidtransService $midtransService)
    {
        $this->midtransService = $midtransService;

        // Add middleware for authentication and timeout
        $this->middleware('auth')->except(['handleNotification']);
        $this->middleware('throttle:10,1')->only(['createPayment']); // Max 10 requests per minute
    }

    /**
     * Test payment gateway (for debugging)
     */
    public function testPayment(Request $request, Transaction $transaction)
    {
        try {
            Log::info('Test payment endpoint called', [
                'transaction_id' => $transaction->id,
                'request_method' => $request->method(),
                'user_id' => auth()->id()
            ]);

            // Test basic functionality
            $testResults = [
                'controller_working' => true,
                'transaction_found' => $transaction ? true : false,
                'transaction_has_items' => $transaction->items->count() > 0,
                'midtrans_config_exists' => !empty(config('midtrans.server_key')),
                'user_authenticated' => auth()->check(),
                'csrf_token_present' => $request->header('X-CSRF-TOKEN') ? true : false
            ];

            return response()->json([
                'success' => true,
                'message' => 'Test endpoint working',
                'timestamp' => now()->toDateTimeString(),
                'test_results' => $testResults,
                'transaction' => [
                    'id' => $transaction->id,
                    'invoice_number' => $transaction->invoice_number,
                    'status' => $transaction->status,
                    'total_amount' => $transaction->total_amount,
                    'items_count' => $transaction->items->count(),
                    'payment_method' => $transaction->payment_method
                ],
                'config' => [
                    'server_key' => substr(config('midtrans.server_key'), 0, 10) . '...',
                    'client_key' => substr(config('midtrans.client_key'), 0, 10) . '...',
                    'merchant_id' => config('midtrans.merchant_id'),
                    'is_production' => config('midtrans.is_production')
                ],
                'request_info' => [
                    'method' => $request->method(),
                    'url' => $request->fullUrl(),
                    'user_agent' => $request->userAgent(),
                    'ip' => $request->ip()
                ]
            ]);
        } catch (\Exception $e) {
            Log::error('Test payment endpoint failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Test failed: ' . $e->getMessage(),
                'error_class' => get_class($e),
                'timestamp' => now()->toDateTimeString()
            ], 500);
        }
    }

    /**
     * Create payment via Midtrans
     */
    public function createPayment(Request $request, Transaction $transaction)
    {
        try {
            // Set timeout for this request
            set_time_limit(60); // 60 seconds max

            Log::info('Creating payment for transaction', [
                'transaction_id' => $transaction->id,
                'status' => $transaction->status,
                'total_amount' => $transaction->total_amount,
                'items_count' => $transaction->items->count(),
                'request_method' => $request->method(),
                'request_headers' => $request->headers->all(),
                'user_agent' => $request->userAgent()
            ]);

            // Early response for debugging
            if ($request->has('debug')) {
                return response()->json([
                    'success' => true,
                    'message' => 'Debug mode - controller reached',
                    'transaction_id' => $transaction->id,
                    'status' => $transaction->status
                ]);
            }

            // Validate transaction
            if ($transaction->status !== 'pending') {
                Log::warning('Transaction status invalid', [
                    'transaction_id' => $transaction->id,
                    'current_status' => $transaction->status
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Transaksi tidak dapat diproses. Status: ' . $transaction->status
                ], 400);
            }

            // Validate transaction has items
            if ($transaction->items->count() === 0) {
                Log::warning('Transaction has no items', [
                    'transaction_id' => $transaction->id
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Transaksi tidak memiliki item'
                ], 400);
            }

            // Create Snap token
            Log::info('Creating Midtrans snap token', [
                'transaction_id' => $transaction->id,
                'start_time' => now()->toDateTimeString()
            ]);

            $startTime = microtime(true);
            $result = $this->midtransService->createSnapToken($transaction);
            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            Log::info('Midtrans snap token creation completed', [
                'transaction_id' => $transaction->id,
                'duration_ms' => $duration,
                'success' => $result['success'] ?? false
            ]);

            if (!$result['success']) {
                Log::error('Failed to create snap token', [
                    'transaction_id' => $transaction->id,
                    'result' => $result
                ]);

                return response()->json($result, 400);
            }

            Log::info('Payment gateway created successfully', [
                'transaction_id' => $transaction->id,
                'snap_token' => substr($result['snap_token'], 0, 20) . '...',
                'redirect_url' => $result['redirect_url']
            ]);

            return response()->json([
                'success' => true,
                'snap_token' => $result['snap_token'],
                'redirect_url' => $result['redirect_url'],
                'transaction_id' => $transaction->id
            ]);

        } catch (\Exception $e) {
            Log::error('Payment creation failed', [
                'transaction_id' => $transaction->id ?? 'unknown',
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            // Return specific error messages
            $message = 'Gagal membuat pembayaran';
            if (strpos($e->getMessage(), 'timeout') !== false) {
                $message = 'Koneksi timeout. Silakan coba lagi.';
            } elseif (strpos($e->getMessage(), 'CSRF') !== false) {
                $message = 'Token keamanan expired. Silakan refresh halaman.';
            } elseif (strpos($e->getMessage(), 'Midtrans') !== false) {
                $message = 'Gagal menghubungi payment gateway. Periksa koneksi internet.';
            }

            return response()->json([
                'success' => false,
                'message' => $message,
                'error_detail' => $e->getMessage(),
                'error_code' => $e->getCode()
            ], 500);
        }
    }

    /**
     * Handle Midtrans notification webhook
     */
    public function handleNotification(Request $request)
    {
        try {
            $notification = $request->all();
            
            Log::info('Midtrans notification received', $notification);

            $result = $this->midtransService->handleNotification($notification);

            if ($result['success']) {
                return response('OK', 200);
            } else {
                return response('FAILED', 400);
            }

        } catch (\Exception $e) {
            Log::error('Notification handling failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return response('ERROR', 500);
        }
    }

    /**
     * Payment finish page
     */
    public function paymentFinish(Request $request)
    {
        $orderId = $request->get('order_id');
        $statusCode = $request->get('status_code');
        $transactionStatus = $request->get('transaction_status');

        Log::info('Payment finish callback', [
            'order_id' => $orderId,
            'status_code' => $statusCode,
            'transaction_status' => $transactionStatus
        ]);

        // Find transaction
        $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

        if (!$transaction) {
            return redirect()->route('transactions.index')
                           ->with('error', 'Transaksi tidak ditemukan');
        }

        // Check if payment is successful
        if (in_array($transactionStatus, ['capture', 'settlement'])) {
            return redirect()->route('transactions.show', $transaction)
                           ->with('success', 'Pembayaran berhasil! Transaksi telah dikonfirmasi.');
        } else {
            return redirect()->route('transactions.show', $transaction)
                           ->with('warning', 'Pembayaran sedang diproses. Status akan diperbarui secara otomatis.');
        }
    }

    /**
     * Payment unfinish page
     */
    public function paymentUnfinish(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::info('Payment unfinish callback', ['order_id' => $orderId]);

        $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

        if ($transaction) {
            return redirect()->route('transactions.show', $transaction)
                           ->with('warning', 'Pembayaran belum selesai. Silakan lanjutkan pembayaran atau pilih metode lain.');
        }

        return redirect()->route('transactions.index')
                       ->with('warning', 'Pembayaran belum selesai');
    }

    /**
     * Payment error page
     */
    public function paymentError(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::error('Payment error callback', [
            'order_id' => $orderId,
            'request' => $request->all()
        ]);

        $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();

        if ($transaction) {
            return redirect()->route('transactions.show', $transaction)
                           ->with('error', 'Terjadi kesalahan dalam pembayaran. Silakan coba lagi atau pilih metode pembayaran lain.');
        }

        return redirect()->route('transactions.index')
                       ->with('error', 'Terjadi kesalahan dalam pembayaran');
    }

    /**
     * Check payment status
     */
    public function checkPaymentStatus(Transaction $transaction)
    {
        try {
            Log::info('Checking payment status', [
                'transaction_id' => $transaction->id,
                'payment_gateway' => $transaction->payment_gateway,
                'order_id' => $transaction->payment_gateway_order_id,
                'current_status' => $transaction->status
            ]);

            // Validate transaction exists and has payment gateway
            if (!$transaction || !$transaction->payment_gateway) {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaksi tidak menggunakan payment gateway'
                ], 400);
            }

            if (empty($transaction->payment_gateway_order_id)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Order ID tidak ditemukan. Transaksi mungkin belum diproses dengan benar.'
                ], 400);
            }

            // If transaction is already completed, return current status
            if ($transaction->status === 'completed') {
                return response()->json([
                    'success' => true,
                    'transaction_status' => $transaction->status,
                    'payment_status' => $transaction->payment_gateway_status,
                    'message' => 'Transaksi sudah selesai'
                ]);
            }

            $result = $this->midtransService->checkTransactionStatus($transaction->payment_gateway_order_id);

            if ($result['success']) {
                // Update transaction status based on current status
                $status = $result['data'];

                Log::info('Updating transaction from Midtrans status', [
                    'transaction_id' => $transaction->id,
                    'midtrans_status' => $status
                ]);

                $this->midtransService->handleNotification((array) $status);

                // Reload transaction to get updated status
                $transaction->refresh();

                return response()->json([
                    'success' => true,
                    'transaction_status' => $transaction->status,
                    'payment_status' => $transaction->payment_gateway_status,
                    'message' => 'Status pembayaran berhasil diperbarui',
                    'data' => $status
                ]);
            }

            Log::warning('Failed to check payment status', [
                'transaction_id' => $transaction->id,
                'result' => $result
            ]);

            return response()->json([
                'success' => false,
                'message' => $result['message'] ?? 'Gagal memeriksa status pembayaran',
                'error_detail' => $result['error_detail'] ?? null
            ], 400);

        } catch (\Exception $e) {
            Log::error('Status check failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan sistem saat mengecek status pembayaran',
                'error_detail' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel payment
     */
    public function cancelPayment(Transaction $transaction)
    {
        try {
            DB::beginTransaction();

            // Return items to inventory if transaction was pending
            if ($transaction->status === 'pending') {
                foreach ($transaction->items as $item) {
                    if ($item->processed_inventory_id) {
                        $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                        if ($product) {
                            $product->increment('current_stock', $item->quantity);
                        }
                    } else {
                        $product = \App\Models\OtherProduct::find($item->product_id);
                        if ($product) {
                            $product->increment('current_stock', $item->quantity);
                        }
                    }
                }
            }

            // Update transaction status
            $transaction->update([
                'status' => 'cancelled',
                'payment_gateway_status' => 'cancelled'
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Pembayaran berhasil dibatalkan'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            Log::error('Payment cancellation failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Gagal membatalkan pembayaran'
            ], 500);
        }
    }
}
