<?php

echo "🧪 TESTING NEW MIDTRANS PAYMENT GATEWAY\n";
echo "=======================================\n\n";

$baseUrl = 'http://127.0.0.1:8000';

// Test 1: Check if Laravel server is running
echo "1. Checking Laravel server...\n";
$response = @file_get_contents($baseUrl);
if ($response !== false) {
    echo "   ✅ Laravel server is running\n\n";
} else {
    echo "   ❌ Laravel server is not accessible\n";
    echo "   💡 Make sure to run: php artisan serve --host=127.0.0.1 --port=8000\n\n";
    exit(1);
}

// Test 2: Check payment routes
echo "2. Testing payment routes...\n";

$routes = [
    'Payment notification' => $baseUrl . '/payment/notification',
    'Payment finish' => $baseUrl . '/payment/finish',
    'Payment unfinish' => $baseUrl . '/payment/unfinish',
    'Payment error' => $baseUrl . '/payment/error'
];

foreach ($routes as $name => $url) {
    $response = @file_get_contents($url);
    if ($response !== false) {
        echo "   ✅ {$name}: Accessible\n";
    } else {
        echo "   ❌ {$name}: Not accessible\n";
    }
}

echo "\n";

// Test 3: Check Midtrans configuration
echo "3. Checking Midtrans configuration...\n";

// Read .env file
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    
    $configs = [
        'MIDTRANS_SERVER_KEY' => 'SB-Mid-server-C-NGkKU1xkdJjFL1REGcrOGl',
        'MIDTRANS_CLIENT_KEY' => 'SB-Mid-client-1rSes3vtWLL8PxZb',
        'MIDTRANS_MERCHANT_ID' => 'G583489924'
    ];
    
    foreach ($configs as $key => $expectedValue) {
        if (strpos($envContent, $key . '=' . $expectedValue) !== false) {
            echo "   ✅ {$key}: Configured correctly\n";
        } else {
            echo "   ❌ {$key}: Not found or incorrect\n";
        }
    }
} else {
    echo "   ❌ .env file not found\n";
}

echo "\n";

// Test 4: Check if files exist
echo "4. Checking payment gateway files...\n";

$files = [
    'MidtransService' => 'app/Services/MidtransService.php',
    'PaymentController' => 'app/Http/Controllers/PaymentController.php',
    'Payment finish view' => 'resources/views/payment/finish.blade.php',
    'Payment unfinish view' => 'resources/views/payment/unfinish.blade.php',
    'Payment error view' => 'resources/views/payment/error.blade.php'
];

foreach ($files as $name => $file) {
    if (file_exists($file)) {
        echo "   ✅ {$name}: File exists\n";
    } else {
        echo "   ❌ {$name}: File missing\n";
    }
}

echo "\n";

// Test 5: Test Midtrans connectivity
echo "5. Testing Midtrans connectivity...\n";

$midtransUrl = 'https://api.sandbox.midtrans.com/v2/ping';
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $midtransUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($endTime - $startTime) * 1000, 2);

if ($error) {
    echo "   ❌ Connection error: {$error}\n";
} else {
    echo "   ✅ Midtrans API accessible\n";
    echo "   📊 Response time: {$duration}ms\n";
    echo "   📄 HTTP Code: {$httpCode}\n";
}

echo "\n";

// Test 6: Instructions for testing
echo "6. Testing instructions...\n";
echo "   📋 To test the payment gateway:\n";
echo "   \n";
echo "   1. 🌐 Open browser and go to: {$baseUrl}/pos\n";
echo "   2. 🔐 Login with your credentials\n";
echo "   3. 🛒 Add some products to cart\n";
echo "   4. 👤 Fill customer name (required)\n";
echo "   5. 💳 Click 'Bayar dengan Midtrans' button\n";
echo "   6. ⏳ Wait for Midtrans payment page to open\n";
echo "   7. 💰 Complete payment using test credentials\n";
echo "   \n";
echo "   🧪 Midtrans Test Credentials:\n";
echo "   - Credit Card: 4811 1111 1111 1114\n";
echo "   - Expiry: 01/25\n";
echo "   - CVV: 123\n";
echo "   - OTP: 112233\n";
echo "   \n";
echo "   📱 For other payment methods:\n";
echo "   - BCA VA: Use any amount\n";
echo "   - Gopay: Use test phone number\n";
echo "   - QRIS: Scan QR code with test app\n";

echo "\n🏁 Test completed!\n\n";

echo "💡 TROUBLESHOOTING:\n";
echo "===================\n";
echo "1. If payment gateway button is disabled:\n";
echo "   - Make sure you're logged in\n";
echo "   - Check browser console for errors\n";
echo "   - Verify CSRF token is present\n\n";

echo "2. If getting 'loading forever':\n";
echo "   - Check Laravel logs: storage/logs/laravel.log\n";
echo "   - Verify Midtrans credentials in .env\n";
echo "   - Check internet connection\n\n";

echo "3. If payment page doesn't open:\n";
echo "   - Check if popup blocker is enabled\n";
echo "   - Look for JavaScript errors in console\n";
echo "   - Verify transaction was created successfully\n\n";

echo "🎉 EXPECTED FLOW:\n";
echo "=================\n";
echo "1. User clicks 'Bayar dengan Midtrans'\n";
echo "2. Transaction is created in database\n";
echo "3. Midtrans Snap token is generated\n";
echo "4. User is redirected to Midtrans payment page\n";
echo "5. User completes payment\n";
echo "6. Midtrans sends notification to webhook\n";
echo "7. Transaction status is updated\n";
echo "8. User sees success/failure page\n\n";
