<?php $__env->startSection('title', 'Laporan Distribusi Ubi ke Pasar'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .simple-card {
        background: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }

    .summary-box {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 25px;
        text-align: center;
        margin-bottom: 20px;
    }

    .summary-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin: 10px 0;
    }

    .summary-label {
        font-size: 1.1rem;
        opacity: 0.9;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-4">Laporan Distribusi Ubi ke Pasar</h1>
        <a href="<?php echo e(route('expiry-recommendations.index')); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i> Kembali
        </a>
    </div>

    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="<?php echo e(route('expiry-recommendations.index')); ?>">Rekomendasi Ubi</a></li>
        <li class="breadcrumb-item active">Laporan Distribusi</li>
    </ol>

    <!-- Filter Section -->
    <div class="simple-card">
        <h5 class="mb-3">Filter Laporan</h5>
        <form method="GET" action="<?php echo e(route('expiry-recommendations.sales-report')); ?>">
            <div class="row g-3">
                <div class="col-md-4">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" class="form-control" id="start_date" name="start_date"
                           value="<?php echo e(request('start_date', now()->startOfMonth()->format('Y-m-d'))); ?>">
                </div>
                <div class="col-md-4">
                    <label for="end_date" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="end_date" name="end_date"
                           value="<?php echo e(request('end_date', now()->format('Y-m-d'))); ?>">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i> Tampilkan
                    </button>
                    <a href="<?php echo e(route('expiry-recommendations.sales-report')); ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-refresh me-1"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Summary Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="summary-box">
                <div class="summary-label">Total Ubi Didistribusikan</div>
                <div class="summary-number"><?php echo e(number_format($totalQuantitySold)); ?></div>
                <div class="summary-label">Unit</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="summary-box" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="summary-label">Jumlah Pasar Tujuan</div>
                <div class="summary-number"><?php echo e($totalMarkets); ?></div>
                <div class="summary-label">Pasar</div>
            </div>
        </div>
    </div>

    <!-- Simple Distribution List -->
    <div class="simple-card">
        <h5 class="mb-3"><i class="fas fa-list me-2"></i> Daftar Distribusi Ubi ke Pasar</h5>

        <?php if($distributions->count() > 0): ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>Tanggal</th>
                            <th>Pasar Tujuan</th>
                            <th>Produk Ubi</th>
                            <th>Jumlah</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $distributions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $distribution): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php $__currentLoopData = $distribution->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <tr>
                                    <td><?php echo e($distribution->distribution_date->format('d/m/Y')); ?></td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                        <strong><?php echo e($distribution->destination); ?></strong>
                                    </td>
                                    <td><?php echo e($item->processedInventory->name ?? ($item->otherProduct->name ?? 'Ubi Bakar')); ?></td>
                                    <td>
                                        <span class="badge bg-success"><?php echo e(number_format($item->quantity)); ?> unit</span>
                                    </td>
                                    <td>
                                        <?php if($distribution->status === 'delivered'): ?>
                                            <span class="badge bg-success">✓ Selesai</span>
                                        <?php elseif($distribution->status === 'in_transit'): ?>
                                            <span class="badge bg-warning">🚚 Dalam Perjalanan</span>
                                        <?php elseif($distribution->status === 'returned'): ?>
                                            <span class="badge bg-danger">↩️ Dikembalikan</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">📋 Direncanakan</span>
                                        <?php endif; ?>

                                        <?php if($distribution->is_urgent ?? false): ?>
                                            <span class="badge bg-danger ms-1">⚡ URGENT</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada data distribusi</h5>
                <p class="text-muted">Belum ada distribusi ubi dalam periode yang dipilih.</p>
            </div>
        <?php endif; ?>
    </div>

    <!-- Summary by Market -->
    <?php if($marketData && $marketData->count() > 0): ?>
    <div class="simple-card">
        <h5 class="mb-3"><i class="fas fa-chart-bar me-2"></i> Ringkasan per Pasar</h5>
        <div class="row">
            <?php $__currentLoopData = $marketData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $market): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="col-md-4 mb-3">
                    <div class="card border-primary">
                        <div class="card-body text-center">
                            <h6 class="card-title"><?php echo e($market->market); ?></h6>
                            <h4 class="text-primary"><?php echo e(number_format($market->total_quantity)); ?></h4>
                            <small class="text-muted">unit ubi terdistribusi</small>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\WEB\iterasi 2\ubi-bakar-cilembu\resources\views/inventory/sales-report.blade.php ENDPATH**/ ?>