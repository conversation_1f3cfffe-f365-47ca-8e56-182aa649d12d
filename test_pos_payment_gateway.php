<?php

require_once 'vendor/autoload.php';

// Load Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Models\User;
use App\Services\MidtransService;
use Illuminate\Support\Facades\Log;

echo "🚀 Testing POS Payment Gateway Integration\n\n";

try {
    // Test 1: Check Prerequisites
    echo "1. Checking Prerequisites...\n";
    
    $user = User::first();
    $product = ProcessedInventory::where('current_stock', '>', 0)->first();
    
    if (!$user) {
        echo "   ❌ No users found. Please create a user first.\n";
        exit(1);
    }
    
    if (!$product) {
        echo "   ❌ No products with stock found. Please add products first.\n";
        exit(1);
    }
    
    echo "   ✅ User found: {$user->name}\n";
    echo "   ✅ Product found: {$product->name} (Stock: {$product->current_stock})\n\n";

    // Test 2: Create Transaction via POS Flow
    echo "2. Creating Transaction (POS Flow)...\n";
    
    $transactionData = [
        'user_id' => $user->id,
        'customer_name' => 'Test Customer POS',
        'customer_phone' => '081234567890',
        'subtotal' => 50000,
        'tax' => 0,
        'discount' => 0,
        'total_amount' => 50000,
        'amount_paid' => 0,
        'change_amount' => 0,
        'payment_method' => 'gateway',
        'status' => 'pending',
        'notes' => 'Test POS payment gateway - ' . now()->format('H:i:s'),
        'invoice_number' => 'POS-TEST-' . time()
    ];
    
    $transaction = Transaction::create($transactionData);
    
    // Create transaction items
    $itemData = [
        'transaction_id' => $transaction->id,
        'processed_inventory_id' => $product->id,
        'product_name' => $product->name,
        'price' => 25000,
        'quantity' => 2,
        'subtotal' => 50000
    ];
    
    TransactionItem::create($itemData);
    
    echo "   ✅ Transaction created: {$transaction->invoice_number}\n";
    echo "   📄 Transaction ID: {$transaction->id}\n";
    echo "   💰 Total Amount: Rp " . number_format($transaction->total_amount, 0, ',', '.') . "\n\n";

    // Test 3: Test Payment Gateway Creation
    echo "3. Testing Payment Gateway Creation...\n";
    
    $midtransService = new MidtransService();
    $result = $midtransService->createSnapToken($transaction);
    
    if ($result['success']) {
        echo "   ✅ Snap token created successfully!\n";
        echo "   🔗 Payment URL: {$result['redirect_url']}\n";
        echo "   🎫 Snap Token: " . substr($result['snap_token'], 0, 30) . "...\n\n";
        
        // Test 4: Test API Endpoint
        echo "4. Testing API Endpoint...\n";
        
        $url = "http://localhost:8000/payment/create/{$transaction->id}";
        echo "   🌐 Testing URL: {$url}\n";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json',
            'X-Requested-With: XMLHttpRequest'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($response && $httpCode === 200) {
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                echo "   ✅ API endpoint working correctly!\n";
                echo "   📱 API Response: Payment URL generated\n\n";
            } else {
                echo "   ❌ API returned error: " . ($data['message'] ?? 'Unknown error') . "\n\n";
            }
        } else {
            echo "   ❌ API request failed. HTTP Code: {$httpCode}\n";
            echo "   📝 Response: " . substr($response, 0, 200) . "...\n\n";
        }
        
        // Test 5: Display Test Results
        echo "5. Test Results Summary:\n";
        echo "   🆔 Transaction ID: {$transaction->id}\n";
        echo "   📄 Invoice Number: {$transaction->invoice_number}\n";
        echo "   💰 Amount: Rp " . number_format($transaction->total_amount, 0, ',', '.') . "\n";
        echo "   🔗 Payment URL: {$result['redirect_url']}\n";
        echo "   📱 Status: {$transaction->status}\n\n";
        
        echo "6. Next Steps for Manual Testing:\n";
        echo "   1. Start Laravel server: php artisan serve\n";
        echo "   2. Open POS: http://localhost:8000/pos\n";
        echo "   3. Add products to cart\n";
        echo "   4. Select 'Payment Gateway' method\n";
        echo "   5. Complete transaction\n";
        echo "   6. Test with cards:\n";
        echo "      ✅ Success: 4811 1111 1111 1114\n";
        echo "      ❌ Failed:  4911 1111 1111 1113\n";
        echo "      ⏳ Pending: 4411 1111 1111 1118\n";
        echo "      CVV: 123, Exp: 01/25\n\n";
        
        echo "✅ POS Payment Gateway Test COMPLETED SUCCESSFULLY!\n";
        echo "🎉 All components are working correctly.\n";
        
    } else {
        echo "   ❌ Failed to create snap token: {$result['message']}\n";
        
        // Clean up
        $transaction->items()->delete();
        $transaction->delete();
        
        echo "\n❌ Test FAILED. Please check Midtrans configuration.\n";
        exit(1);
    }

} catch (Exception $e) {
    echo "❌ Test Error: {$e->getMessage()}\n";
    echo "📍 File: {$e->getFile()}:{$e->getLine()}\n";
    
    // Clean up if needed
    if (isset($transaction)) {
        $transaction->items()->delete();
        $transaction->delete();
    }
    
    exit(1);
}

echo "\n🎯 Ready for production testing!\n";
