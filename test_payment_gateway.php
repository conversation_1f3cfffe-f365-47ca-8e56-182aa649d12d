<?php

/**
 * Test script untuk Payment Gateway Midtrans
 * Jalankan dengan: php test_payment_gateway.php
 */

require_once 'vendor/autoload.php';

use Midtrans\Config;
use Midtrans\Snap;

// Konfigurasi Midtrans
Config::$serverKey = 'Mid-server-fpJV8ASMK23zQ3xuEPX4a49S';
Config::$isProduction = false;
Config::$isSanitized = true;
Config::$is3ds = true;

echo "=== TESTING MIDTRANS PAYMENT GATEWAY ===\n\n";

// Test 1: Konfigurasi
echo "1. Testing Konfigurasi...\n";
echo "Server Key: " . (Config::$serverKey ? "✓ Set" : "✗ Not Set") . "\n";
echo "Environment: " . (Config::$isProduction ? "Production" : "Sandbox") . "\n";
echo "Sanitized: " . (Config::$isSanitized ? "Yes" : "No") . "\n";
echo "3DS: " . (Config::$is3ds ? "Enabled" : "Disabled") . "\n\n";

// Test 2: Create Snap Token
echo "2. Testing Create Snap Token...\n";

$params = [
    'transaction_details' => [
        'order_id' => 'TEST-' . time(),
        'gross_amount' => 50000,
    ],
    'item_details' => [
        [
            'id' => 'ubi-original-1',
            'price' => 25000,
            'quantity' => 2,
            'name' => 'Ubi Bakar Original',
            'category' => 'Food'
        ]
    ],
    'customer_details' => [
        'first_name' => 'Test Customer',
        'phone' => '081234567890',
    ],
    'enabled_payments' => [
        'credit_card',
        'mandiri_clickpay',
        'cimb_clicks',
        'bca_klikbca',
        'bca_klikpay',
        'bri_epay',
        'echannel',
        'permata_va',
        'bca_va',
        'bni_va',
        'other_va',
        'gopay',
        'indomaret',
        'alfamart'
    ],
    'callbacks' => [
        'finish' => 'http://localhost/transactions/payment/finish',
        'unfinish' => 'http://localhost/transactions/payment/unfinish',
        'error' => 'http://localhost/transactions/payment/error',
    ]
];

try {
    $snapToken = Snap::getSnapToken($params);
    echo "✓ Snap Token berhasil dibuat: " . substr($snapToken, 0, 20) . "...\n";
    
    $redirectUrl = 'https://app.sandbox.midtrans.com/snap/v2/vtweb/' . $snapToken;
    echo "✓ Redirect URL: " . $redirectUrl . "\n\n";
    
    echo "3. Testing Payment URL...\n";
    echo "Silakan buka URL berikut untuk test pembayaran:\n";
    echo $redirectUrl . "\n\n";
    
    echo "=== TEST CARDS FOR SANDBOX ===\n";
    echo "Success Payment:\n";
    echo "- Card Number: 4811 1111 1111 1114\n";
    echo "- CVV: 123\n";
    echo "- Exp: 01/25\n\n";
    
    echo "Failed Payment:\n";
    echo "- Card Number: 4911 1111 1111 1113\n";
    echo "- CVV: 123\n";
    echo "- Exp: 01/25\n\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n\n";
}

echo "=== WEBHOOK TESTING ===\n";
echo "Untuk test webhook, gunakan ngrok atau expose localhost:\n";
echo "1. Install ngrok: https://ngrok.com/\n";
echo "2. Jalankan: ngrok http 80\n";
echo "3. Set webhook URL di Midtrans Dashboard:\n";
echo "   https://your-ngrok-url.ngrok.io/api/midtrans/notification\n\n";

echo "=== MIDTRANS DASHBOARD ===\n";
echo "Login ke: https://dashboard.sandbox.midtrans.com/\n";
echo "Email: <EMAIL>\n";
echo "Password: your-password\n\n";

echo "Test selesai!\n";
