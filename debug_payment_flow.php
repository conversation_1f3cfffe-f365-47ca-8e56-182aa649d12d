<?php

echo "🔍 DEBUGGING PAYMENT GATEWAY FLOW\n";
echo "=================================\n\n";

$baseUrl = 'http://127.0.0.1:8000';

// Test 1: Check if we can create a simple transaction
echo "1. Testing transaction creation...\n";

// Get CSRF token first
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/pos');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'debug_cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'debug_cookies.txt');

$posResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    echo "   ❌ Cannot access POS page: HTTP {$httpCode}\n";
    echo "   💡 Make sure you're logged in\n\n";
} else {
    echo "   ✅ POS page accessible\n";
    
    // Extract CSRF token
    preg_match('/name="csrf-token" content="([^"]+)"/', $posResponse, $matches);
    $csrfToken = $matches[1] ?? '';
    
    if ($csrfToken) {
        echo "   ✅ CSRF token found: " . substr($csrfToken, 0, 20) . "...\n\n";
        
        // Test 2: Create a test transaction
        echo "2. Creating test transaction...\n";
        
        $transactionData = [
            '_token' => $csrfToken,
            'items' => json_encode([
                [
                    'id' => '1',
                    'type' => 'processed',
                    'name' => 'Test Ubi Bakar',
                    'price' => 25000,
                    'quantity' => 2,
                    'subtotal' => 50000
                ]
            ]),
            'payment_method' => 'gateway',
            'amount_paid' => '0',
            'customer_name' => 'Test Customer',
            'customer_phone' => '081234567890',
            'notes' => 'Test transaction for debugging',
            'use_payment_gateway' => '1'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $baseUrl . '/transactions');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_COOKIEFILE, 'debug_cookies.txt');
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($transactionData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-Requested-With: XMLHttpRequest',
            'Accept: application/json'
        ]);
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $endTime = microtime(true);
        
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        $duration = round(($endTime - $startTime) * 1000, 2);
        
        if ($error) {
            echo "   ❌ cURL Error: {$error}\n";
        } else {
            echo "   📊 Response: HTTP {$httpCode} ({$duration}ms)\n";
            echo "   📄 Response: " . substr($response, 0, 200) . "...\n";
            
            $responseData = json_decode($response, true);
            if ($responseData) {
                echo "   📋 JSON Response:\n";
                echo "      Success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
                
                if (isset($responseData['transaction']['id'])) {
                    $transactionId = $responseData['transaction']['id'];
                    echo "      Transaction ID: {$transactionId}\n\n";
                    
                    // Test 3: Test payment gateway creation
                    echo "3. Testing payment gateway creation...\n";
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $baseUrl . "/payment/create/{$transactionId}");
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                    curl_setopt($ch, CURLOPT_COOKIEFILE, 'debug_cookies.txt');
                    curl_setopt($ch, CURLOPT_HTTPHEADER, [
                        'Content-Type: application/json',
                        'Accept: application/json',
                        'X-CSRF-TOKEN: ' . $csrfToken,
                        'X-Requested-With: XMLHttpRequest'
                    ]);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
                    
                    echo "   🚀 Making payment gateway request...\n";
                    $startTime = microtime(true);
                    
                    $paymentResponse = curl_exec($ch);
                    $endTime = microtime(true);
                    
                    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    $error = curl_error($ch);
                    curl_close($ch);
                    
                    $duration = round(($endTime - $startTime) * 1000, 2);
                    
                    if ($error) {
                        echo "   ❌ Payment Gateway Error: {$error}\n";
                    } else {
                        echo "   📊 Payment Response: HTTP {$httpCode} ({$duration}ms)\n";
                        
                        if ($duration > 10000) {
                            echo "   ⚠️  WARNING: Very slow response ({$duration}ms)\n";
                        } elseif ($duration > 5000) {
                            echo "   ⚠️  WARNING: Slow response ({$duration}ms)\n";
                        } else {
                            echo "   ✅ Good response time\n";
                        }
                        
                        echo "   📄 Response: " . substr($paymentResponse, 0, 300) . "...\n";
                        
                        $paymentData = json_decode($paymentResponse, true);
                        if ($paymentData) {
                            echo "   📋 Payment JSON Response:\n";
                            echo "      Success: " . ($paymentData['success'] ? 'true' : 'false') . "\n";
                            
                            if (isset($paymentData['message'])) {
                                echo "      Message: {$paymentData['message']}\n";
                            }
                            
                            if (isset($paymentData['redirect_url'])) {
                                echo "      ✅ Redirect URL: {$paymentData['redirect_url']}\n";
                            }
                            
                            if (isset($paymentData['error_detail'])) {
                                echo "      ❌ Error Detail: {$paymentData['error_detail']}\n";
                            }
                        }
                    }
                } else {
                    echo "      ❌ No transaction ID in response\n";
                }
            } else {
                echo "   ❌ Invalid JSON response\n";
            }
        }
    } else {
        echo "   ❌ CSRF token not found\n";
    }
}

// Cleanup
@unlink('debug_cookies.txt');

echo "\n🏁 Debug completed!\n\n";

echo "💡 ANALYSIS:\n";
echo "============\n";
echo "1. If transaction creation fails: Check authentication and validation\n";
echo "2. If payment gateway hangs: Check Midtrans configuration and network\n";
echo "3. If getting errors: Check Laravel logs for detailed error messages\n";
echo "4. If response is slow: Check server performance and Midtrans API\n\n";

echo "🔧 NEXT STEPS:\n";
echo "==============\n";
echo "1. Check Laravel logs: tail -f storage/logs/laravel.log\n";
echo "2. Test in browser with F12 console open\n";
echo "3. Verify Midtrans credentials are correct\n";
echo "4. Check if all required fields are filled\n\n";
