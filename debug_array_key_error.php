<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 DEBUGGING ARRAY KEY ERROR\n";
echo "============================\n\n";

// Test 1: Check if product ID 1003 exists
echo "1. Checking product ID 1003...\n";

try {
    $processedProduct = \App\Models\ProcessedInventory::find(1003);
    if ($processedProduct) {
        echo "   ✅ ProcessedInventory ID 1003 found: {$processedProduct->name}\n";
        echo "      Stock: {$processedProduct->current_stock}\n";
        echo "      Active: " . ($processedProduct->is_active ? 'Yes' : 'No') . "\n";
    } else {
        echo "   ❌ ProcessedInventory ID 1003 NOT FOUND\n";
    }
    
    $otherProduct = \App\Models\OtherProduct::find(1003);
    if ($otherProduct) {
        echo "   ✅ OtherProduct ID 1003 found: {$otherProduct->name}\n";
        echo "      Stock: {$otherProduct->current_stock}\n";
        echo "      Active: " . ($otherProduct->is_active ? 'Yes' : 'No') . "\n";
    } else {
        echo "   ❌ OtherProduct ID 1003 NOT FOUND\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking product: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check recent transactions for problematic data
echo "2. Checking recent transactions...\n";

try {
    $recentTransactions = \App\Models\Transaction::with('items')
        ->orderBy('created_at', 'desc')
        ->limit(5)
        ->get();
        
    foreach ($recentTransactions as $transaction) {
        echo "   Transaction ID: {$transaction->id}\n";
        echo "   Items count: " . $transaction->items->count() . "\n";
        
        foreach ($transaction->items as $item) {
            echo "      - Product ID: {$item->product_id}\n";
            echo "        Processed ID: {$item->processed_inventory_id}\n";
            echo "        Name: {$item->product_name}\n";
        }
        echo "\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking transactions: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check for orphaned transaction items
echo "3. Checking for orphaned transaction items...\n";

try {
    $orphanedItems = \App\Models\TransactionItem::whereNotNull('processed_inventory_id')
        ->whereDoesntHave('processedInventory')
        ->limit(10)
        ->get();
        
    if ($orphanedItems->count() > 0) {
        echo "   ⚠️  Found {$orphanedItems->count()} orphaned items:\n";
        foreach ($orphanedItems as $item) {
            echo "      - Item ID: {$item->id}, Product ID: {$item->processed_inventory_id}\n";
        }
    } else {
        echo "   ✅ No orphaned items found\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking orphaned items: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Check available products
echo "4. Checking available products...\n";

try {
    $processedCount = \App\Models\ProcessedInventory::where('is_active', true)->count();
    $otherCount = \App\Models\OtherProduct::where('is_active', true)->count();
    
    echo "   📊 Active ProcessedInventory: {$processedCount}\n";
    echo "   📊 Active OtherProduct: {$otherCount}\n";
    
    // Show ID ranges
    $minProcessed = \App\Models\ProcessedInventory::min('id');
    $maxProcessed = \App\Models\ProcessedInventory::max('id');
    $minOther = \App\Models\OtherProduct::min('id');
    $maxOther = \App\Models\OtherProduct::max('id');
    
    echo "   📊 ProcessedInventory ID range: {$minProcessed} - {$maxProcessed}\n";
    echo "   📊 OtherProduct ID range: {$minOther} - {$maxOther}\n";
    
} catch (\Exception $e) {
    echo "   ❌ Error checking products: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🏁 Debug completed!\n\n";

echo "💡 POSSIBLE CAUSES:\n";
echo "===================\n";
echo "1. Product ID 1003 was deleted but still referenced in cart/session\n";
echo "2. JavaScript is trying to access a product that doesn't exist\n";
echo "3. Old transaction data referencing non-existent products\n";
echo "4. Cache issue with product data\n\n";

echo "🔧 SOLUTIONS:\n";
echo "=============\n";
echo "1. Clear browser cache and localStorage\n";
echo "2. Add proper error handling in TransactionController\n";
echo "3. Validate product existence before processing\n";
echo "4. Clean up orphaned transaction items\n\n";

echo "🚀 IMMEDIATE FIXES:\n";
echo "==================\n";
echo "1. Refresh the POS page (F5)\n";
echo "2. Clear cart and start fresh\n";
echo "3. Check if product ID 1003 exists in database\n";
echo "4. Add try-catch blocks in payment processing\n\n";
