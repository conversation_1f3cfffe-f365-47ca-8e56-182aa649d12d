@extends('layouts.app')

@section('styles')
<style>
    /* Modern POS Styling */
    :root {
        --card-shadow: 0 4px 15px rgba(0,0,0,0.08);
        --hover-shadow: 0 8px 20px rgba(0,0,0,0.12);
        --border-radius: 16px;
        --cart-border-radius: 20px;
        --primary-gradient: linear-gradient(135deg, #4e73df 0%, #3c5cc9 100%);
        --success-gradient: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
        --danger-gradient: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        --product-card-height: 220px;
    }
    
    .pos-layout {
        background-color: #f8fafc;
        min-height: calc(100vh - 80px);
    }
    
    .pos-container {
        background-color: #ffffff;
        border-radius: var(--border-radius);
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: var(--card-shadow);
        height: calc(100vh - 160px);
        overflow-y: auto;
        scrollbar-width: thin;
        border: 1px solid #e9ecef;
    }
    
    .pos-container::-webkit-scrollbar {
        width: 6px;
    }
    
    .pos-container::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
    }
    
    .product-card {
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        box-shadow: var(--card-shadow);
        border-radius: 12px;
        overflow: hidden;
        position: relative;
        background: white;
    }

    .product-image-container {
        position: relative;
        height: 180px;
        overflow: hidden;
    }

    .product-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .product-card:hover .product-image {
        transform: scale(1.05);
    }

    .product-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
    }

    .product-image-placeholder {
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        position: relative;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--hover-shadow);
    }
    
    .product-card .card-body {
        padding: 15px;
        display: flex;
        flex-direction: column;
    }
    
    .product-image {
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;
        background-color: #f8f9fa;
        border-radius: 8px;
    }
    
    .product-image i {
        font-size: 30px;
        color: #4e73df;
    }
    
    .product-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 8px;
        height: 40px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    
    .product-price {
        font-size: 18px;
        font-weight: 700;
        color: #4e73df;
        margin-bottom: 8px;
    }
    
    .product-stock {
        font-size: 13px;
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    .cart-container {
        background-color: white;
        border-radius: var(--cart-border-radius);
        box-shadow: var(--card-shadow);
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    .cart-header {
        padding: 18px 20px;
        background: var(--primary-gradient);
        color: white;
        font-weight: 600;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .cart-header .cart-title {
        display: flex;
        align-items: center;
    }
    
    .cart-header .cart-title i {
        font-size: 20px;
        margin-right: 10px;
    }
    
    .cart-header .cart-count {
        background: rgba(255, 255, 255, 0.3);
        padding: 5px 10px;
        border-radius: 30px;
        font-size: 14px;
        font-weight: 500;
    }
    
    .cart-body {
        flex: 1;
        padding: 0;
        overflow-y: auto;
        max-height: 350px;
        scrollbar-width: thin;
    }
    
    .cart-body::-webkit-scrollbar {
        width: 4px;
    }
    
    .cart-body::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 2px;
    }
    
    .cart-footer {
        padding: 20px;
        border-top: 1px solid #eee;
        background-color: #f8f9fa;
    }
    
    .cart-item {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .cart-item-details {
        flex: 1;
    }
    
    .cart-item-name {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .cart-item-price {
        color: var(--primary-color);
        font-weight: 500;
    }
    
    .cart-quantity-controls {
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .quantity-btn {
        width: 30px;
        height: 30px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
    
    .quantity-input {
        width: 50px;
        text-align: center;
        border-radius: 5px;
        padding: 5px;
        width: fit-content;
    }
    
    .qty-btn {
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        background: #ffffff;
        border-radius: 5px;
        font-size: 14px;
        color: #4e73df;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        cursor: pointer;
    }
    
    .qty-btn:hover {
        background: #4e73df;
        color: white;
    }
    
    .cart-item-qty {
        width: 35px;
        height: 28px;
        text-align: center;
        margin: 0 5px;
        border: none;
        background: transparent;
        font-weight: 600;
        color: #333;
    }
    
    .cart-footer {
        padding: 15px;
        border-top: 1px solid #eee;
    }
    
    .cart-summary {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
    }
    
    .summary-total {
        font-size: 18px;
        font-weight: 700;
        color: var(--primary-color);
    }
    
    .category-filter {
        display: flex;
        gap: 10px;
        overflow-x: auto;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }
    
    .category-btn {
        padding: 10px 20px;
        border-radius: 25px;
        background-color: #f1f3f5;
        color: #495057;
        white-space: nowrap;
        border: 2px solid transparent;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        margin-right: 10px;
        margin-bottom: 10px;
    }
    
    .category-btn.active {
        background-color: var(--primary-color);
        color: white;
    }
    
    .search-box {
        position: relative;
        margin-bottom: 15px;
    }
    
    .search-box i {
        position: absolute;
        left: 12px;
        top: 12px;
        color: #adb5bd;
    }
    
    .search-input {
        padding-left: 35px;
        border-radius: 50px;
        border: 1px solid #ced4da;
    }
    
    .payment-box {
        background-color: white;
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
        padding: 0;
        margin-top: 20px;
        overflow: hidden;
        display: none;
    }
    
    .payment-header {
        background: var(--primary-gradient);
        color: white;
        padding: 15px 20px;
        font-weight: 600;
        font-size: 18px;
        display: flex;
        align-items: center;
    }
    
    .payment-header i {
        margin-right: 10px;
    }
    
    .payment-body {
        padding: 20px;
    }
    
    .payment-method-tabs {
        display: flex;
        margin-bottom: 20px;
        background: #f8f9fa;
        border-radius: 10px;
        padding: 5px;
    }
    
    .payment-method-tab {
        flex: 1;
        padding: 12px;
        cursor: pointer;
        font-weight: 500;
        color: #666;
        transition: all 0.3s ease;
        text-align: center;
        border-radius: 8px;
    }
    
    .payment-method-tab:hover {
        background-color: rgba(78, 115, 223, 0.1);
        color: #4e73df;
    }
    
    .payment-method-tab.active {
        color: white;
        background: var(--primary-gradient);
        box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
    }
    
    .payment-method-tabs {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
    }

    .payment-method-tab {
        flex: 1;
        padding: 15px 10px;
        text-align: center;
        border: 2px solid #e9ecef;
        border-radius: 12px;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        font-weight: 500;
        color: #6c757d;
    }

    .payment-method-tab:hover {
        border-color: #667eea;
        color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .payment-method-tab.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-color: #667eea;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    }

    .payment-method-tab i {
        display: block;
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .payment-method-section {
        display: none;
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
    }
    
    .number-pad {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 10px;
        margin-top: 20px;
        max-width: 280px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .btn-number {
        width: 100%;
        height: 60px;
        font-weight: 600;
        font-size: 22px;
        border-radius: 12px;
        border: none;
        background-color: #f8f9fa;
        color: #333;
        transition: all 0.3s ease;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }
    
    .btn-number:hover {
        background-color: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .btn-number:active {
        transform: translateY(1px);
    }
    
    .btn-number.clear {
        background: var(--danger-gradient);
        color: white;
    }
    
    .btn-number.zero {
        grid-column: span 2;
    }
    
    .btn-action {
        margin-top: 20px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 15px;
    }
    
    .btn-exact {
        background: #f8f9fa;
        color: #4e73df;
        border: 2px solid #4e73df;
        border-radius: 10px;
        padding: 12px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-exact:hover {
        background: rgba(78, 115, 223, 0.1);
    }
    
    .payment-method-section.active {
        display: block;
    }

    .payment-option {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .payment-option:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }

    .tiny {
        font-size: 0.75rem;
    }
    
    .btn-number {
        width: 60px;
        height: 60px;
        margin: 5px;
        font-weight: bold;
        border-radius: 10px;
        border: 1px solid #ced4da;
        background-color: white;
    }
    
    .btn-number:hover {
        background-color: #f1f3f5;
    }
    
    .transaction-btn {
        height: 50px;
        border-radius: 8px;
        font-weight: 600;
    }
    
    #qris-section img {
        max-width: 200px;
        margin: 10px auto;
        display: block;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    #empty-cart {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 0;
        color: #adb5bd;
    }
</style>
    .payment-status {
        padding: 10px;
        margin: 10px 0;
        border-radius: 5px;
        display: none;
    }
    .payment-status.success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .payment-status.pending {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeeba;
    }
    .payment-status.error {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .debug-info {
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        padding: 10px;
        margin-bottom: 15px;
        border-radius: 5px;
    }
</style>
@endsection

@section('content')
<div class="container-fluid pos-layout py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="page-title">
            <i class="fas fa-shopping-cart"></i> Kasir (POS)
        </h1>
        <div>
            <button class="btn btn-outline-primary me-2" id="refresh-btn" title="Refresh Halaman">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <a href="{{ route('transactions.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-list"></i> Riwayat Transaksi
            </a>
        </div>
    </div>

    <!-- Debug Information -->
    <div class="debug-info">
        <h5>Debug Info:</h5>
        <p>Total Products: {{ count($products) }}</p>
        <p>Processed Products: {{ $products->filter(function($p) { return $p instanceof \App\Models\ProcessedInventory; })->count() }}</p>
        <p>Other Products: {{ $products->filter(function($p) { return $p instanceof \App\Models\OtherProduct; })->count() }}</p>
        <p>Categories: {{ implode(', ', $categories->pluck('name')->toArray()) }}</p>
    </div>

    <div class="row g-4">
        <!-- Products List (Expanded) -->
        <div class="col-xl-8 col-lg-7 mb-4">
            <div class="pos-container">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-box me-2"></i>Daftar Produk
                    </h5>
                    <span class="badge bg-primary fs-6 px-3 py-2">
                        {{ count($products) }} Produk Tersedia
                    </span>
                </div>

                <div class="search-box mb-4">
                    <div class="input-group">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" id="search-product" class="form-control border-start-0" placeholder="Cari produk...">
                    </div>
                </div>

                <div class="category-filter mb-4">
                    <button class="category-btn active" data-category="all">
                        <i class="fas fa-th-large me-1"></i>Semua Produk
                    </button>
                    @foreach($categories as $category)
                    <button class="category-btn" data-category="{{ $category->id }}">
                        <i class="fas fa-tag me-1"></i>{{ $category->name }}
                    </button>
                    @endforeach
                </div>
                
                <div class="row g-3" id="products-container">
                    @foreach($products as $product)
                    <div class="col-lg-4 col-md-6 col-sm-6 product-item"
                        data-id="{{ $product->id }}"
                        data-name="{{ $product->name }}"
                        data-price="{{ $product->selling_price }}"
                        data-category="{{ $product instanceof \App\Models\ProcessedInventory ? $product->product_type : $product->category }}"
                        data-stock="{{ $product->current_stock }}"
                        data-type="{{ $product instanceof \App\Models\ProcessedInventory ? 'processed' : 'other' }}">
                        <div class="card product-card h-100">
                            @if($product->image)
                                <div class="product-image-container">
                                    <img src="{{ asset('images/products/' . $product->image) }}"
                                         alt="{{ $product->name }}"
                                         class="product-image">
                                    <div class="product-overlay">
                                        <span class="badge {{ $product instanceof \App\Models\ProcessedInventory ? 'bg-success' : 'bg-info' }}">
                                            {{ $product instanceof \App\Models\ProcessedInventory ? 'Ubi Bakar' : 'Produk Lain' }}
                                        </span>
                                    </div>
                                </div>
                            @else
                                <div class="product-image-placeholder">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                    <span class="badge {{ $product instanceof \App\Models\ProcessedInventory ? 'bg-success' : 'bg-info' }} position-absolute top-0 end-0 m-2">
                                        {{ $product instanceof \App\Models\ProcessedInventory ? 'Ubi Bakar' : 'Produk Lain' }}
                                    </span>
                                </div>
                            @endif
                            <div class="card-body d-flex flex-column">
                                <h6 class="product-name mb-2">{{ $product->name }}</h6>
                                <p class="product-price fw-bold text-primary mb-2">Rp {{ number_format($product->selling_price, 0, ',', '.') }}</p>
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <span class="product-stock small {{ $product->current_stock <= 5 ? 'text-danger' : 'text-success' }}">
                                        <i class="fas fa-box me-1"></i>Stok: {{ $product->current_stock }}
                                    </span>
                                </div>
                                <button class="btn btn-primary w-100 add-to-cart mt-auto"
                                        data-id="{{ $product->id }}"
                                        data-type="{{ $product instanceof \App\Models\ProcessedInventory ? 'processed' : 'other' }}"
                                        {{ $product->current_stock <= 0 ? 'disabled' : '' }}>
                                    <i class="fas fa-plus me-1"></i>
                                    {{ $product->current_stock <= 0 ? 'Stok Habis' : 'Tambah ke Keranjang' }}
                                </button>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>

        <!-- Cart & Payment (Compact) -->
        <div class="col-xl-4 col-lg-5 mb-4">
            <div class="cart-container">
                <div class="cart-header">
                    <div class="cart-title">
                        <i class="fas fa-shopping-basket"></i> Keranjang Belanja
                    </div>
                    <div class="cart-count" id="cart-badge">0 item</div>
                </div>
                
                <div class="cart-body">
                    <div id="cart-items">
                        <!-- Cart items will be populated here -->
                    </div>
                    
                    <div id="empty-cart" class="text-center py-5">
                        <i class="fas fa-shopping-cart fa-3x mb-3 text-muted"></i>
                        <p class="fw-bold mb-1">Keranjang belanja kosong</p>
                        <p class="small text-muted">Pilih produk untuk menambahkan ke keranjang</p>
                    </div>
                </div>
                
                <div class="cart-footer">
                    <div class="cart-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span id="subtotal" class="fw-bold">Rp 0</span>
                        </div>
                        <div class="summary-row">
                            <span>Pajak (0%):</span>
                            <span id="tax" class="fw-bold">Rp 0</span>
                        </div>
                        <div class="summary-row summary-total">
                            <span>Total:</span>
                            <span id="total" class="fw-bold">Rp 0</span>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button id="process-payment-btn" class="btn btn-lg transaction-btn" style="background: var(--primary-gradient); color: white;" disabled>
                            <i class="fas fa-money-bill-wave me-2"></i> Proses Pembayaran
                        </button>
                        <button id="clear-cart-btn" class="btn btn-outline-danger transaction-btn">
                            <i class="fas fa-trash me-2"></i> Kosongkan Keranjang
                        </button>
                    </div>
                </div>
            </div>

            <!-- Payment Box -->
            <div class="payment-box mt-4" id="payment-box">
                <div class="payment-header">
                    <i class="fas fa-credit-card"></i> Pembayaran
                </div>
                
                <div class="payment-body">
                    <div class="payment-method-tabs mb-4">
                        <div class="payment-method-tab active" data-method="cash">
                            <i class="fas fa-money-bill-alt me-2"></i> Tunai
                        </div>
                        <div class="payment-method-tab" data-method="gateway">
                            <i class="fas fa-credit-card me-2"></i> Payment Gateway
                        </div>
                        <div class="payment-method-tab" data-method="qris">
                            <i class="fas fa-qrcode me-2"></i> QRIS Manual
                        </div>
                    </div>

                <!-- Cash Payment Section -->
                <div class="payment-method-section active" id="cash-section">
                    <div class="mb-3">
                        <label for="amount-due" class="form-label">Total Tagihan</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="text" class="form-control" id="amount-due" readonly>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="amount-paid" class="form-label">Jumlah Dibayar</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="text" class="form-control" id="amount-paid">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="change" class="form-label">Kembalian</label>
                        <div class="input-group">
                            <span class="input-group-text">Rp</span>
                            <input type="text" class="form-control" id="change" readonly>
                        </div>
                    </div>
            
                    <div class="text-end mb-3">
                        <button id="exact-amount" class="btn-exact">
                            <i class="fas fa-coins me-1"></i> Uang Pas
                        </button>
                    </div>
            
                    <div class="number-pad">
                        <button class="btn-number" data-number="1">1</button>
                        <button class="btn-number" data-number="2">2</button>
                        <button class="btn-number" data-number="3">3</button>
                        <button class="btn-number" data-number="4">4</button>
                        <button class="btn-number" data-number="5">5</button>
                        <button class="btn-number" data-number="6">6</button>
                        <button class="btn-number" data-number="7">7</button>
                        <button class="btn-number" data-number="8">8</button>
                        <button class="btn-number" data-number="9">9</button>
                        <button class="btn-number zero" data-number="0">0</button>
                        <button class="btn-number" data-number="000">000</button>
                        <button class="btn-number clear" data-number="C">C</button>
                    </div>
            
                    <div class="d-grid mt-4">
                        <button id="complete-cash-payment" class="btn btn-lg" style="background: var(--success-gradient); color: white;" disabled>
                            <i class="fas fa-check-circle me-2"></i> Selesaikan Pembayaran
                        </button>
                    </div>
                </div>



                <!-- QRIS Payment Section -->
                <div class="payment-method-section" id="qris-section">
                    <div class="text-center p-4 bg-light rounded-3 mb-4">
                        <img src="{{ asset('images/qris-sample.png') }}" alt="QRIS Code" class="img-fluid mb-3" style="max-width: 200px;">
                        <p class="mb-1 fw-bold">Scan QRIS code di atas untuk melakukan pembayaran</p>
                        <p class="small text-muted">Silakan buka aplikasi e-wallet atau mobile banking Anda</p>
                    </div>
                    <div class="payment-status alert alert-info mb-4" id="qris-status">
                        <i class="fas fa-info-circle me-2"></i> Menunggu pembayaran via QRIS
                    </div>
                    <div class="d-grid mt-4">
                        <button id="complete-qris-payment" class="btn btn-lg" style="background: var(--success-gradient); color: white;">
                            <i class="fas fa-check-circle me-2"></i> Konfirmasi Pembayaran
                        </button>
                    </div>
                </div>

                <!-- Payment Gateway Section -->
                <div class="payment-method-section" id="gateway-section">
                    <div class="text-center p-4 mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; color: white;">
                        <i class="fas fa-credit-card fa-3x mb-3"></i>
                        <h5 class="mb-2 fw-bold">Payment Gateway Midtrans</h5>
                        <p class="mb-0 opacity-75">Pembayaran online yang aman dan terpercaya</p>
                    </div>

                    <div class="row g-3 mb-4">
                        <div class="col-6">
                            <div class="payment-option p-3 border rounded-3 text-center h-100" style="border: 2px solid #e3f2fd; background: #f8f9ff;">
                                <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                                <div class="small fw-semibold">Kartu Kredit/Debit</div>
                                <div class="tiny text-muted">Visa, Mastercard</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="payment-option p-3 border rounded-3 text-center h-100" style="border: 2px solid #e8f5e8; background: #f8fff8;">
                                <i class="fas fa-university fa-2x text-success mb-2"></i>
                                <div class="small fw-semibold">Virtual Account</div>
                                <div class="tiny text-muted">BCA, Mandiri, BNI, BRI</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="payment-option p-3 border rounded-3 text-center h-100" style="border: 2px solid #fff3e0; background: #fffaf5;">
                                <i class="fas fa-mobile-alt fa-2x text-warning mb-2"></i>
                                <div class="small fw-semibold">E-Wallet</div>
                                <div class="tiny text-muted">GoPay, OVO, DANA</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="payment-option p-3 border rounded-3 text-center h-100" style="border: 2px solid #e1f5fe; background: #f0f9ff;">
                                <i class="fas fa-store fa-2x text-info mb-2"></i>
                                <div class="small fw-semibold">Convenience Store</div>
                                <div class="tiny text-muted">Indomaret, Alfamart</div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info border-0 mb-4" style="background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%); color: white;">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-shield-alt fa-2x me-3"></i>
                            <div>
                                <div class="fw-bold">Pembayaran Aman & Terjamin</div>
                                <div class="small opacity-75">Transaksi dilindungi dengan enkripsi SSL 256-bit</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid">
                        <button id="complete-gateway-payment" class="btn btn-lg fw-bold" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 12px; padding: 15px;">
                            <i class="fas fa-arrow-right me-2"></i> Lanjutkan ke Payment Gateway
                        </button>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="customer-info mt-4">
                    <div class="customer-info-header mb-3">
                        <i class="fas fa-user me-2"></i> Informasi Pelanggan
                    </div>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="customer-name" class="form-label">Nama Pelanggan</label>
                            <input type="text" class="form-control shadow-sm" id="customer-name" placeholder="Opsional">
                        </div>
                        <div class="col-md-6">
                            <label for="customer-phone" class="form-label">No. HP</label>
                            <input type="text" class="form-control shadow-sm" id="customer-phone" placeholder="Opsional">
                        </div>
                        <div class="col-12">
                            <label for="transaction-note" class="form-label">Catatan</label>
                            <textarea class="form-control shadow-sm" id="transaction-note" rows="2" placeholder="Catatan transaksi..."></textarea>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <button class="btn btn-lg btn-outline-secondary" id="cancel-payment-btn">
                            <i class="fas fa-arrow-left me-2"></i> Kembali
                        </button>
                        <button class="btn btn-lg" style="background: var(--primary-gradient); color: white;" id="complete-transaction-btn" disabled>
                            <i class="fas fa-check me-2"></i> Selesaikan Transaksi
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Complete Transaction Form -->
    <form id="transaction-form" action="{{ route('transactions.store') }}" method="POST" style="display: none;">
        @csrf
        <input type="hidden" name="items" id="items-input">
        <input type="hidden" name="payment_method" id="payment-method-input">
        <input type="hidden" name="amount_paid" id="amount-paid-input">
        <input type="hidden" name="customer_name" id="customer-name-input">
        <input type="hidden" name="customer_phone" id="customer-phone-input">
        <input type="hidden" name="notes" id="notes-input">
        <input type="hidden" name="use_payment_gateway" id="use-payment-gateway-input">
    </form>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('POS System initialized');

        // Cart data
        let cart = [];
        let totalAmount = 0;
        
        // DOM elements
        const productsContainer = document.getElementById('products-container');
        const cartItems = document.getElementById('cart-items');
        const emptyCart = document.getElementById('empty-cart');
        const totalItemsElement = document.getElementById('total-items');
        const totalAmountElement = document.getElementById('total-amount');
        const processPaymentBtn = document.getElementById('process-payment-btn');
        const paymentBox = document.getElementById('payment-box');
        const amountDueElement = document.getElementById('amount-due');
        const amountPaidElement = document.getElementById('amount-paid');
        const changeElement = document.getElementById('change');
        const cancelPaymentBtn = document.getElementById('cancel-payment-btn');
        const completeTransactionBtn = document.getElementById('complete-transaction-btn');
        const transactionForm = document.getElementById('transaction-form');
        const searchInput = document.getElementById('search-product');
        const categoryButtons = document.querySelectorAll('.category-btn');
        const paymentMethodTabs = document.querySelectorAll('.payment-method-tab');
        const paymentSections = document.querySelectorAll('.payment-method-section');
        let currentPaymentMethod = 'cash';
        
        // Product search functionality
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const products = document.querySelectorAll('.product-item');
            
            products.forEach(product => {
                const name = product.getAttribute('data-name').toLowerCase();
                if (name.includes(searchTerm)) {
                    product.style.display = '';
                } else {
                    product.style.display = 'none';
                }
            });
        });
        
        // Category filter
        categoryButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons
                categoryButtons.forEach(btn => btn.classList.remove('active'));
                // Add active class to clicked button
                this.classList.add('active');
                
                const category = this.getAttribute('data-category');
                const products = document.querySelectorAll('.product-item');
                
                products.forEach(product => {
                    if (category === 'all' || product.getAttribute('data-category') === category) {
                        product.style.display = '';
                    } else {
                        product.style.display = 'none';
                    }
                });
            });
        });
        
        // Add to cart functionality
        productsContainer.addEventListener('click', function(e) {
            if (e.target.classList.contains('add-to-cart')) {
                const productCard = e.target.closest('.product-item');
                const productId = productCard.getAttribute('data-id');
                const productName = productCard.getAttribute('data-name');
                const productPrice = parseInt(productCard.getAttribute('data-price'));
                const maxStock = parseInt(productCard.getAttribute('data-stock'));
                const productType = productCard.getAttribute('data-type') || 'processed';
                
                // Check if product already in cart
                const existingItem = cart.find(item => item.id === productId && item.type === productType);
                
                if (existingItem) {
                    if (existingItem.quantity < maxStock) {
                        existingItem.quantity += 1;
                    } else {
                        alert('Stok tidak mencukupi!');
                        return;
                    }
                } else {
                    if (maxStock > 0) {
                        cart.push({
                            id: productId,
                            name: productName,
                            price: productPrice,
                            quantity: 1,
                            max_stock: maxStock,
                            type: productType
                        });
                    } else {
                        alert('Stok tidak tersedia!');
                        return;
                    }
                }
                
                renderCart();
            }
        });
        
        // Render cart items
        function renderCart() {
            if (cart.length === 0) {
                emptyCart.style.display = 'block';
                cartItems.innerHTML = '';
                processPaymentBtn.disabled = true;
            } else {
                emptyCart.style.display = 'none';
                cartItems.innerHTML = '';
                
                cart.forEach(item => {
                    const cartItem = document.createElement('div');
                    cartItem.className = 'cart-item';
                    cartItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6>${item.name}</h6>
                                <p class="text-muted">Rp ${formatNumber(item.price)} x ${item.quantity}</p>
                            </div>
                            <div class="d-flex align-items-center">
                                <button class="btn btn-sm btn-outline-danger me-2 decrease-qty" data-id="${item.id}" data-type="${item.type}">
                                    <i class="fas fa-minus"></i>
                                </button>
                                <input type="number" class="form-control quantity-input" value="${item.quantity}" min="1" max="${item.max_stock}" data-id="${item.id}" data-type="${item.type}">
                                <button class="btn btn-sm btn-outline-primary ms-2 increase-qty" data-id="${item.id}" data-type="${item.type}">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger ms-3 remove-item" data-id="${item.id}" data-type="${item.type}">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    `;
                    cartItems.appendChild(cartItem);
                });
                
                // Add event listeners to quantity buttons and inputs
                addQuantityEventListeners();
                processPaymentBtn.disabled = false;
            }
            
            updateCartSummary();
        }
        
        // Add event listeners to quantity buttons and inputs
        function addQuantityEventListeners() {
            // Decrease quantity buttons
            document.querySelectorAll('.decrease-qty').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-id');
                    const productType = this.getAttribute('data-type');
                    const item = cart.find(item => item.id === productId && item.type === productType);
                    
                    if (item && item.quantity > 1) {
                        item.quantity -= 1;
                        renderCart();
                    }
                });
            });
            
            // Increase quantity buttons
            document.querySelectorAll('.increase-qty').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-id');
                    const productType = this.getAttribute('data-type');
                    const item = cart.find(item => item.id === productId && item.type === productType);
                    
                    if (item && item.quantity < item.max_stock) {
                        item.quantity += 1;
                        renderCart();
                    } else {
                        alert('Stok tidak mencukupi!');
                    }
                });
            });
            
            // Quantity input fields
            document.querySelectorAll('.quantity-input').forEach(input => {
                input.addEventListener('change', function() {
                    const productId = this.getAttribute('data-id');
                    const productType = this.getAttribute('data-type');
                    const item = cart.find(item => item.id === productId && item.type === productType);
                    const newQuantity = parseInt(this.value);
                    
                    if (item && newQuantity >= 1 && newQuantity <= item.max_stock) {
                        item.quantity = newQuantity;
                        renderCart();
                    } else {
                        alert('Jumlah tidak valid atau melebihi stok!');
                        renderCart(); // Re-render to reset invalid input
                    }
                });
            });
            
            // Remove item buttons
            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.getAttribute('data-id');
                    const productType = this.getAttribute('data-type');
                    cart = cart.filter(item => !(item.id === productId && item.type === productType));
                    renderCart();
                });
            });
        }
        
        // Update cart summary
        function updateCartSummary() {
            const totalItems = cart.reduce((total, item) => total + item.quantity, 0);
            totalAmount = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

            // Update cart badge
            const cartBadge = document.getElementById('cart-badge');
            if (cartBadge) {
                cartBadge.textContent = totalItems + ' item' + (totalItems !== 1 ? 's' : '');
            }

            // Update total displays
            const subtotalElement = document.getElementById('subtotal');
            const taxElement = document.getElementById('tax');
            const totalElement = document.getElementById('total');

            if (subtotalElement) subtotalElement.textContent = `Rp ${formatNumber(totalAmount)}`;
            if (taxElement) taxElement.textContent = 'Rp 0';
            if (totalElement) totalElement.textContent = `Rp ${formatNumber(totalAmount)}`;

            if (amountDueElement) {
                amountDueElement.value = formatNumber(totalAmount);
            }
        }
        
        // Payment method tab functionality
        paymentMethodTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // Remove active class from all tabs
                paymentMethodTabs.forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                this.classList.add('active');

                // Get payment method
                currentPaymentMethod = this.getAttribute('data-method');

                // Show appropriate section
                showPaymentSection(currentPaymentMethod);
                updateCompleteButtonState();

                console.log('Payment method changed to:', currentPaymentMethod);
            });
        });

        // Payment Gateway Button Handler
        const completeGatewayBtn = document.getElementById('complete-gateway-payment');
        if (completeGatewayBtn) {
            completeGatewayBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Payment Gateway button clicked');

                if (cart.length === 0) {
                    alert('Keranjang belanja kosong! Silakan tambahkan produk terlebih dahulu.');
                    return;
                }

                // Set payment method to gateway
                currentPaymentMethod = 'gateway';

                // Disable button and show loading
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Memproses Payment Gateway...';

                // Submit transaction with gateway
                setTimeout(() => {
                    submitTransactionForGateway();
                }, 500);
            });
        }

        // Process payment button
        processPaymentBtn.addEventListener('click', function() {
            paymentBox.style.display = 'block';
            amountDueElement.value = formatNumber(totalAmount);

            // Reset payment form
            currentPaymentMethod = 'cash';
            amountPaidElement.value = '';
            changeElement.value = '';
            document.getElementById('transaction-note').value = '';
            document.getElementById('customer-name').value = '';
            document.getElementById('customer-phone').value = '';

            // Reset tabs to cash
            paymentMethodTabs.forEach(tab => {
                tab.classList.remove('active');
                if (tab.getAttribute('data-method') === 'cash') {
                    tab.classList.add('active');
                }
            });

            // Show cash section by default
            showPaymentSection('cash');

            // Reset payment status
            document.querySelectorAll('.payment-status').forEach(status => {
                status.style.display = 'none';
                status.textContent = '';
                status.className = 'payment-status';
            });

            updateCompleteButtonState();
        });
        

        
        // Show appropriate payment section
        function showPaymentSection(method) {
            paymentSections.forEach(section => {
                section.classList.remove('active');
            });
            
            const activeSection = document.getElementById(`${method}-section`);
            if (activeSection) {
                activeSection.classList.add('active');
            }
            
            // Reset status displays
            document.querySelectorAll('.payment-status').forEach(status => {
                status.style.display = 'none';
                status.textContent = '';
                status.className = 'payment-status';
            });
            
            // Enable/disable complete button based on payment method
            updateCompleteButtonState();
        }
        
        // Update complete button state based on payment method and inputs
        function updateCompleteButtonState() {
            let isValid = false;

            switch (currentPaymentMethod) {
                case 'cash':
                    const amountPaid = parseNumber(amountPaidElement.value);
                    isValid = amountPaid >= totalAmount;
                    break;

                case 'qris':
                    // For QRIS, we'll simulate payment verification
                    simulateQRISPayment();
                    isValid = true;
                    break;

                case 'gateway':
                    // For payment gateway, always valid
                    isValid = true;
                    break;
            }

            completeTransactionBtn.disabled = !isValid;
        }
        
        // Simulate QRIS payment verification
        function simulateQRISPayment() {
            const qrisStatus = document.getElementById('qris-status');
            qrisStatus.style.display = 'block';
            qrisStatus.className = 'payment-status pending';
            qrisStatus.textContent = 'Menunggu pembayaran QRIS...';
            
            // Simulate payment success after 2 seconds
            setTimeout(() => {
                qrisStatus.className = 'payment-status success';
                qrisStatus.textContent = 'Pembayaran QRIS berhasil!';
                completeTransactionBtn.disabled = false;
            }, 2000);
        }
        

        
        // Amount paid input handler
        amountPaidElement.addEventListener('input', function() {
            const amountPaid = parseNumber(this.value);
            const change = amountPaid - totalAmount;
            
            changeElement.value = change >= 0 ? formatNumber(change) : '0';
            updateCompleteButtonState();
        });
        
        // Numpad buttons
        document.querySelectorAll('.btn-number').forEach(button => {
            button.addEventListener('click', function() {
                const number = this.getAttribute('data-number');
                let currentValue = amountPaidElement.value.replace(/\./g, '');
                
                if (number === 'C') {
                    currentValue = '';
                } else {
                    currentValue += number;
                }
                
                amountPaidElement.value = formatNumber(parseInt(currentValue) || 0);
                
                // Trigger input event to calculate change
                const inputEvent = new Event('input', { bubbles: true });
                amountPaidElement.dispatchEvent(inputEvent);
            });
        });
        
        // Clear amount button
        document.getElementById('clear-amount').addEventListener('click', function() {
            amountPaidElement.value = '';
            changeElement.value = '';
            updateCompleteButtonState();
        });
        
        // Exact amount button
        document.getElementById('exact-amount').addEventListener('click', function() {
            amountPaidElement.value = formatNumber(totalAmount);
            changeElement.value = '0';
            updateCompleteButtonState();
        });
        
        // Cancel payment button
        cancelPaymentBtn.addEventListener('click', function() {
            paymentBox.style.display = 'none';
        });
        
        // Complete transaction button
        completeTransactionBtn.addEventListener('click', function() {
            // Check if cart is empty
            if (cart.length === 0) {
                alert('Keranjang belanja kosong!');
                return;
            }

            // Show loading indicator
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Memproses...';

            // Prepare form data
            document.getElementById('items-input').value = JSON.stringify(cart);
            document.getElementById('payment-method-input').value = currentPaymentMethod;
            document.getElementById('amount-paid-input').value = currentPaymentMethod === 'gateway' ? 0 : parseNumber(amountPaidElement.value);
            document.getElementById('customer-name-input').value = document.getElementById('customer-name').value;
            document.getElementById('customer-phone-input').value = document.getElementById('customer-phone').value;
            document.getElementById('notes-input').value = document.getElementById('transaction-note').value;
            document.getElementById('use-payment-gateway-input').value = currentPaymentMethod === 'gateway' ? '1' : '0';
            
            // Add logging for debugging
            console.log('Submitting transaction:', {
                items: cart,
                payment_method: currentPaymentMethod,
                amount_paid: currentPaymentMethod === 'gateway' ? 0 : parseNumber(amountPaidElement.value),
                customer_name: document.getElementById('customer-name').value,
                customer_phone: document.getElementById('customer-phone').value,
                notes: document.getElementById('transaction-note').value,
                use_payment_gateway: currentPaymentMethod === 'gateway'
            });
            
            // Submit the form
            if (currentPaymentMethod === 'gateway') {
                // For payment gateway, submit via AJAX to handle redirect
                submitTransactionWithGateway();
            } else {
                // For traditional payments, submit normally
                transactionForm.submit();
            }
        });

        // Submit transaction specifically for payment gateway
        function submitTransactionForGateway() {
            console.log('submitTransactionForGateway called');

            if (cart.length === 0) {
                alert('Keranjang belanja kosong!');
                return;
            }

            // Prepare form data
            document.getElementById('items-input').value = JSON.stringify(cart);
            document.getElementById('payment-method-input').value = 'gateway';
            document.getElementById('amount-paid-input').value = '0';
            document.getElementById('customer-name-input').value = document.getElementById('customer-name')?.value || '';
            document.getElementById('customer-phone-input').value = document.getElementById('customer-phone')?.value || '';
            document.getElementById('notes-input').value = document.getElementById('transaction-note')?.value || '';
            document.getElementById('use-payment-gateway-input').value = '1';

            console.log('Gateway transaction data:', {
                items: cart,
                payment_method: 'gateway',
                use_payment_gateway: true,
                customer_name: document.getElementById('customer-name')?.value || '',
                customer_phone: document.getElementById('customer-phone')?.value || ''
            });

            // Submit via AJAX for payment gateway
            submitTransactionWithGateway();
        }

        // Submit transaction with payment gateway
        function submitTransactionWithGateway() {
            const formData = new FormData(transactionForm);

            // Show loading state
            completeTransactionBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Membuat transaksi...';

            fetch(transactionForm.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Transaction response:', data);

                if (data.success) {
                    if (data.use_payment_gateway) {
                        // Update loading state
                        completeTransactionBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Membuat payment gateway...';

                        // Create payment via gateway
                        createPaymentGateway(data.transaction.id);
                    } else {
                        // Regular transaction success
                        window.location.href = `/transactions/${data.transaction.id}`;
                    }
                } else {
                    throw new Error(data.message || 'Terjadi kesalahan dalam membuat transaksi');
                }
            })
            .catch(error => {
                console.error('Transaction error:', error);
                alert('Terjadi kesalahan: ' + error.message);

                // Reset buttons
                completeTransactionBtn.disabled = false;
                completeTransactionBtn.innerHTML = '<i class="fas fa-check me-2"></i> Selesaikan Transaksi';

                const gatewayBtn = document.getElementById('complete-gateway-payment');
                if (gatewayBtn) {
                    gatewayBtn.disabled = false;
                    gatewayBtn.innerHTML = '<i class="fas fa-arrow-right me-2"></i> Lanjutkan ke Payment Gateway';
                }
            });
        }

        // Create payment gateway transaction
        function createPaymentGateway(transactionId) {
            console.log('Creating payment gateway for transaction:', transactionId);

            fetch(`/payment/create/${transactionId}`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            })
            .then(response => {
                console.log('Payment gateway response status:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Payment gateway response:', data);

                if (data.success) {
                    // Show success message
                    alert('Payment gateway berhasil dibuat! Anda akan diarahkan ke halaman pembayaran.');

                    // Redirect to Midtrans payment page
                    window.open(data.redirect_url, '_blank');

                    // Also redirect to transaction detail page
                    setTimeout(() => {
                        window.location.href = `/transactions/${transactionId}`;
                    }, 1000);
                } else {
                    throw new Error(data.message || 'Gagal membuat pembayaran');
                }
            })
            .catch(error => {
                console.error('Payment gateway error:', error);
                alert('Terjadi kesalahan dalam membuat pembayaran: ' + error.message);

                // Reset buttons
                completeTransactionBtn.disabled = false;
                completeTransactionBtn.innerHTML = '<i class="fas fa-check me-2"></i> Selesaikan Transaksi';

                const gatewayBtn = document.getElementById('complete-gateway-payment');
                if (gatewayBtn) {
                    gatewayBtn.disabled = false;
                    gatewayBtn.innerHTML = '<i class="fas fa-arrow-right me-2"></i> Lanjutkan ke Payment Gateway';
                }
            });
        }
        
        // Helper function to format numbers with thousand separators
        function formatNumber(number) {
            return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        }
        
        // Helper function to parse formatted number string to integer
        function parseNumber(string) {
            return parseInt(string.replace(/\./g, '')) || 0;
        }
    });
</script>
@endpush
@endsection 