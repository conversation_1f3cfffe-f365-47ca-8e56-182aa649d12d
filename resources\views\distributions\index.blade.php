@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-truck me-2"></i>Daftar Distribusi
                        </h4>
                        <div>
                            <a href="{{ route('distributions.create') }}" class="btn btn-light btn-sm">
                                <i class="fas fa-plus me-1"></i>Distribusi Baru
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    @endif

                    <!-- Filter & Search -->
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="searchInput" placeholder="Cari distribusi...">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="statusFilter">
                                <option value="">Semua Status</option>
                                <option value="planned">Planned</option>
                                <option value="in_transit">In Transit</option>
                                <option value="delivered">Delivered</option>
                                <option value="returned">Returned</option>
                            </select>
                        </div>
                        <div class="col-md-5 text-end">
                            <span class="badge bg-primary fs-6 px-3 py-2">
                                Total: {{ $distributions->total() }} Distribusi
                            </span>
                        </div>
                    </div>

                    <!-- Distributions Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th width="5%">#</th>
                                    <th width="15%">No. Distribusi</th>
                                    <th width="15%">Tanggal</th>
                                    <th width="20%">Tujuan</th>
                                    <th width="15%">Penanggung Jawab</th>
                                    <th width="10%">Total Item</th>
                                    <th width="10%">Status</th>
                                    <th width="10%">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($distributions as $index => $distribution)
                                <tr>
                                    <td>{{ $distributions->firstItem() + $index }}</td>
                                    <td>
                                        <strong>{{ $distribution->distribution_number ?? 'DIST-' . $distribution->id }}</strong>
                                    </td>
                                    <td>
                                        {{ $distribution->distribution_date->format('d/m/Y') }}
                                        <br>
                                        <small class="text-muted">{{ $distribution->distribution_date->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        <strong>{{ $distribution->market_name }}</strong>
                                    </td>
                                    <td>
                                        {{ $distribution->user->name ?? 'N/A' }}
                                        <br>
                                        <small class="text-muted">{{ $distribution->created_at->format('d/m/Y H:i') }}</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info fs-6">{{ $distribution->items->count() }} item</span>
                                        <br>
                                        <small class="text-muted">{{ $distribution->items->sum('quantity') }} qty</small>
                                    </td>
                                    <td>
                                        @php
                                            $statusColors = [
                                                'planned' => 'bg-info',
                                                'in_transit' => 'bg-warning',
                                                'delivered' => 'bg-success',
                                                'returned' => 'bg-danger'
                                            ];
                                            $statusColor = $statusColors[$distribution->status] ?? 'bg-secondary';
                                        @endphp
                                        <span class="badge {{ $statusColor }}">
                                            {{ ucfirst($distribution->status) }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ route('distributions.show', $distribution) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            @if($distribution->status !== 'delivered')
                                            <a href="{{ route('distributions.edit', $distribution) }}" 
                                               class="btn btn-sm btn-outline-warning" 
                                               title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <i class="fas fa-truck fa-3x text-muted mb-3"></i>
                                        <p class="text-muted">Belum ada distribusi yang tercatat</p>
                                        <a href="{{ route('distributions.create') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>Buat Distribusi Pertama
                                        </a>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    @if($distributions->hasPages())
                    <div class="d-flex justify-content-center mt-4">
                        {{ $distributions->links() }}
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const tableRows = document.querySelectorAll('tbody tr');

    function filterTable() {
        const searchTerm = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value.toLowerCase();

        tableRows.forEach(row => {
            if (row.cells.length === 1) return; // Skip empty state row
            
            const distributionNumber = row.cells[1].textContent.toLowerCase();
            const destination = row.cells[3].textContent.toLowerCase();
            const user = row.cells[4].textContent.toLowerCase();
            const status = row.cells[6].textContent.toLowerCase();

            const matchesSearch = distributionNumber.includes(searchTerm) || 
                                destination.includes(searchTerm) || 
                                user.includes(searchTerm);
            const matchesStatus = !statusValue || status.includes(statusValue);

            row.style.display = matchesSearch && matchesStatus ? '' : 'none';
        });
    }

    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
});
</script>
@endsection
