<?php

namespace App\Services;

use App\Models\Transaction;
use Illuminate\Support\Facades\Log;
use Midtrans\Config;
use Midtrans\Snap;

class MidtransService
{
    public function __construct()
    {
        // Configuration will be set in createSnapToken method to prevent conflicts
    }

    /**
     * Create Snap token for payment
     */
    public function createSnapToken(Transaction $transaction)
    {
        try {
            Log::info('Creating Midtrans Snap token', [
                'transaction_id' => $transaction->id,
                'total_amount' => $transaction->total_amount
            ]);

            // Configure Midtrans first
            Config::$serverKey = config('midtrans.server_key');
            Config::$isProduction = config('midtrans.is_production');
            Config::$isSanitized = config('midtrans.is_sanitized');
            Config::$is3ds = config('midtrans.is_3ds');

            // Initialize curlOptions properly to prevent array key errors
            Config::$curlOptions = [
                CURLOPT_TIMEOUT => 30,
                CURLOPT_CONNECTTIMEOUT => 10,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_HTTPHEADER => [] // Initialize empty array to prevent undefined key error
            ];

            // Validate Midtrans configuration
            if (empty(Config::$serverKey)) {
                throw new \Exception('Midtrans server key is not configured');
            }

            // Build transaction parameters
            $params = $this->buildTransactionParams($transaction);

            // Create snap token with error handling
            Log::info('Calling Midtrans Snap::getSnapToken', [
                'params_structure' => [
                    'order_id' => $params['transaction_details']['order_id'],
                    'gross_amount' => $params['transaction_details']['gross_amount'],
                    'items_count' => count($params['item_details']),
                    'customer_name' => $params['customer_details']['first_name']
                ]
            ]);

            $snapToken = Snap::getSnapToken($params);
            
            // Update transaction
            $transaction->update([
                'payment_gateway' => 'midtrans',
                'snap_token' => $snapToken,
                'status' => 'pending'
            ]);

            Log::info('Snap token created successfully', [
                'transaction_id' => $transaction->id,
                'snap_token' => substr($snapToken, 0, 20) . '...'
            ]);

            return [
                'success' => true,
                'snap_token' => $snapToken,
                'redirect_url' => $this->getSnapRedirectUrl($snapToken)
            ];

        } catch (\Exception $e) {
            Log::error('Failed to create Snap token', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'error_class' => get_class($e),
                'error_file' => $e->getFile(),
                'error_line' => $e->getLine(),
                'stack_trace' => $e->getTraceAsString()
            ]);

            // Handle specific error cases
            $errorMessage = $e->getMessage();

            if (strpos($errorMessage, 'Undefined array key') !== false) {
                $errorMessage = 'Terjadi kesalahan dalam data transaksi. Silakan coba lagi dengan produk yang berbeda.';
            } elseif (strpos($errorMessage, 'ServerKey') !== false) {
                $errorMessage = 'Konfigurasi payment gateway bermasalah. Silakan hubungi administrator.';
            } elseif (strpos($errorMessage, 'network') !== false || strpos($errorMessage, 'timeout') !== false) {
                $errorMessage = 'Koneksi ke payment gateway bermasalah. Silakan coba lagi.';
            }

            return [
                'success' => false,
                'message' => 'Gagal membuat token pembayaran: ' . $errorMessage
            ];
        }
    }

    /**
     * Build transaction parameters for Midtrans
     */
    private function buildTransactionParams(Transaction $transaction)
    {
        try {
            $orderId = 'UBI-' . $transaction->id . '-' . time();

            Log::info('Building transaction params', [
                'transaction_id' => $transaction->id,
                'order_id' => $orderId,
                'items_count' => $transaction->items->count()
            ]);

            // Build item details with validation
            $itemDetails = [];
            foreach ($transaction->items as $index => $item) {
                try {
                    // Validate item data
                    if (!$item->product_name || !$item->price || !$item->quantity) {
                        Log::warning('Invalid item data', [
                            'item_id' => $item->id,
                            'product_name' => $item->product_name,
                            'price' => $item->price,
                            'quantity' => $item->quantity
                        ]);
                        continue;
                    }

                    $itemDetails[] = [
                        'id' => 'ITEM-' . $item->id,
                        'price' => (int) $item->price,
                        'quantity' => (int) $item->quantity,
                        'name' => substr($item->product_name, 0, 50), // Limit name length
                        'category' => 'Food'
                    ];

                    Log::debug('Item processed for Midtrans', [
                        'item_id' => $item->id,
                        'name' => $item->product_name,
                        'price' => $item->price,
                        'quantity' => $item->quantity
                    ]);

                } catch (\Exception $e) {
                    Log::error('Error processing item for Midtrans', [
                        'item_id' => $item->id ?? 'unknown',
                        'index' => $index,
                        'error' => $e->getMessage()
                    ]);
                    continue;
                }
            }

            if (empty($itemDetails)) {
                throw new \Exception('No valid items found for transaction');
            }

            // Customer details
            $customerDetails = [
                'first_name' => $transaction->customer_name ?: 'Customer',
                'phone' => $transaction->customer_phone ?: '081234567890'
            ];

            $params = [
                'transaction_details' => [
                    'order_id' => $orderId,
                    'gross_amount' => (int) $transaction->total_amount,
                ],
                'item_details' => $itemDetails,
                'customer_details' => $customerDetails,
                'enabled_payments' => [
                    'credit_card', 'bca_va', 'bni_va', 'bri_va',
                    'echannel', 'permata_va', 'other_va', 'gopay',
                    'shopeepay', 'qris'
                ],
                'callbacks' => [
                    'finish' => url('/payment/finish'),
                    'unfinish' => url('/payment/unfinish'),
                    'error' => url('/payment/error')
                ]
            ];

            Log::info('Transaction params built successfully', [
                'order_id' => $orderId,
                'gross_amount' => $transaction->total_amount,
                'items_count' => count($itemDetails)
            ]);

            return $params;

        } catch (\Exception $e) {
            Log::error('Error building transaction params', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get Snap redirect URL
     */
    private function getSnapRedirectUrl($snapToken)
    {
        $baseUrl = config('midtrans.is_production') 
            ? 'https://app.midtrans.com/snap/v2/vtweb/' 
            : 'https://app.sandbox.midtrans.com/snap/v2/vtweb/';
            
        return $baseUrl . $snapToken;
    }

    /**
     * Handle notification from Midtrans
     */
    public function handleNotification($notification)
    {
        try {
            $orderId = $notification['order_id'];
            $statusCode = $notification['status_code'];
            $grossAmount = $notification['gross_amount'];
            $transactionStatus = $notification['transaction_status'];

            // Extract transaction ID from order ID
            $transactionId = explode('-', $orderId)[1];
            $transaction = Transaction::find($transactionId);

            if (!$transaction) {
                Log::error('Transaction not found for notification', ['order_id' => $orderId]);
                return false;
            }

            // Update transaction status based on Midtrans status
            if ($transactionStatus == 'capture' || $transactionStatus == 'settlement') {
                $transaction->update([
                    'status' => 'completed',
                    'payment_status' => 'paid'
                ]);
            } elseif ($transactionStatus == 'pending') {
                $transaction->update([
                    'status' => 'pending',
                    'payment_status' => 'pending'
                ]);
            } elseif ($transactionStatus == 'deny' || $transactionStatus == 'expire' || $transactionStatus == 'cancel') {
                $transaction->update([
                    'status' => 'cancelled',
                    'payment_status' => 'failed'
                ]);
            }

            Log::info('Notification processed successfully', [
                'order_id' => $orderId,
                'transaction_status' => $transactionStatus,
                'transaction_id' => $transaction->id
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to process notification', [
                'error' => $e->getMessage(),
                'notification' => $notification
            ]);
            return false;
        }
    }
}
