<?php

namespace App\Services;

use App\Models\Transaction;
use Midtrans\Config;
use Midtrans\Snap;
use Midtrans\Transaction as MidtransTransaction;
use Illuminate\Support\Facades\Log;
use Exception;

class MidtransService
{
    public function __construct()
    {
        // Set Midtrans configuration
        Config::$serverKey = config('midtrans.server_key');
        Config::$isProduction = config('midtrans.is_production');
        Config::$isSanitized = config('midtrans.is_sanitized');
        Config::$is3ds = config('midtrans.is_3ds');

        // Set timeout for API calls
        Config::$curlOptions = [
            CURLOPT_TIMEOUT => 30, // 30 seconds timeout
            CURLOPT_CONNECTTIMEOUT => 10, // 10 seconds connection timeout
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false, // For development/sandbox
            CURLOPT_SSL_VERIFYHOST => false, // For development/sandbox
        ];
    }

    /**
     * Create Snap token for transaction
     */
    public function createSnapToken(Transaction $transaction): array
    {
        try {
            $params = $this->buildTransactionParams($transaction);
            
            Log::info('Creating Midtrans Snap token', [
                'transaction_id' => $transaction->id,
                'order_id' => $params['transaction_details']['order_id'],
                'gross_amount' => $params['transaction_details']['gross_amount'],
                'server_key' => substr(config('midtrans.server_key'), 0, 10) . '...',
                'is_production' => config('midtrans.is_production')
            ]);

            // Set timeout for this specific request
            $startTime = microtime(true);

            $snapToken = Snap::getSnapToken($params);

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2); // in milliseconds

            Log::info('Midtrans API call completed', [
                'transaction_id' => $transaction->id,
                'duration_ms' => $duration,
                'snap_token_length' => strlen($snapToken)
            ]);
            
            // Update transaction with snap token
            $transaction->update([
                'payment_gateway' => 'midtrans',
                'payment_gateway_order_id' => $params['transaction_details']['order_id'],
                'snap_token' => $snapToken,
                'payment_gateway_expired_at' => now()->addHours(config('midtrans.transaction_timeout', 24)),
                'status' => 'pending'
            ]);

            $redirectUrl = $this->getSnapRedirectUrl($snapToken);

            Log::info('Snap token created successfully', [
                'transaction_id' => $transaction->id,
                'snap_token' => substr($snapToken, 0, 20) . '...',
                'redirect_url' => $redirectUrl,
                'total_duration_ms' => round((microtime(true) - $startTime) * 1000, 2)
            ]);

            return [
                'success' => true,
                'snap_token' => $snapToken,
                'redirect_url' => $redirectUrl
            ];

        } catch (Exception $e) {
            Log::error('Failed to create Midtrans Snap token', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage(),
                'error_class' => get_class($e)
            ]);

            $message = 'Gagal membuat token pembayaran';
            if (strpos($e->getMessage(), 'timeout') !== false) {
                $message = 'Koneksi ke Midtrans timeout. Silakan coba lagi.';
            } elseif (strpos($e->getMessage(), '401') !== false) {
                $message = 'Konfigurasi Midtrans tidak valid. Periksa kredensial.';
            } elseif (strpos($e->getMessage(), 'network') !== false || strpos($e->getMessage(), 'curl') !== false) {
                $message = 'Masalah koneksi internet. Periksa koneksi dan coba lagi.';
            }

            return [
                'success' => false,
                'message' => $message,
                'error_detail' => $e->getMessage()
            ];
        }
    }

    /**
     * Build transaction parameters for Midtrans
     */
    private function buildTransactionParams(Transaction $transaction): array
    {
        $orderId = 'UBI-' . $transaction->id . '-' . time();
        
        // Build item details
        $itemDetails = [];
        foreach ($transaction->items as $item) {
            // Get product name safely
            $productName = $item->product_name;
            if (empty($productName)) {
                if ($item->processed_inventory_id && $item->processedInventory) {
                    $productName = $item->processedInventory->name;
                } elseif ($item->other_product_id && $item->otherProduct) {
                    $productName = $item->otherProduct->name;
                } else {
                    $productName = 'Produk #' . ($item->processed_inventory_id ?? $item->other_product_id);
                }
            }

            // Generate safe item ID
            $itemId = 'ITEM-' . ($item->id ?? uniqid());
            if ($item->processed_inventory_id) {
                $itemId = 'PROC-' . $item->processed_inventory_id;
            } elseif ($item->other_product_id) {
                $itemId = 'OTHER-' . $item->other_product_id;
            }

            $itemDetails[] = [
                'id' => $itemId,
                'price' => (int) $item->price,
                'quantity' => (int) $item->quantity,
                'name' => $productName,
                'category' => 'Food'
            ];
        }

        // Add tax if any
        if ($transaction->tax > 0) {
            $itemDetails[] = [
                'id' => 'TAX',
                'price' => (int) $transaction->tax,
                'quantity' => 1,
                'name' => 'Pajak'
            ];
        }

        // Subtract discount if any
        if ($transaction->discount > 0) {
            $itemDetails[] = [
                'id' => 'DISCOUNT',
                'price' => -(int) $transaction->discount,
                'quantity' => 1,
                'name' => 'Diskon'
            ];
        }

        $params = [
            'transaction_details' => [
                'order_id' => $orderId,
                'gross_amount' => (int) $transaction->total_amount,
            ],
            'item_details' => $itemDetails,
            'customer_details' => [
                'first_name' => $transaction->customer_name ?? 'Customer',
                'phone' => $transaction->customer_phone ?? '',
            ],
            'enabled_payments' => config('midtrans.enabled_payments'),
            'callbacks' => [
                'finish' => config('midtrans.finish_url'),
                'unfinish' => config('midtrans.unfinish_url'),
                'error' => config('midtrans.error_url'),
            ],
            'expiry' => [
                'start_time' => date('Y-m-d H:i:s O'),
                'unit' => 'hours',
                'duration' => config('midtrans.transaction_timeout', 24)
            ]
        ];

        return $params;
    }

    /**
     * Get Snap redirect URL
     */
    private function getSnapRedirectUrl(string $snapToken): string
    {
        $baseUrl = config('midtrans.is_production') 
            ? 'https://app.midtrans.com/snap/v2/vtweb/'
            : 'https://app.sandbox.midtrans.com/snap/v2/vtweb/';
            
        return $baseUrl . $snapToken;
    }

    /**
     * Handle payment notification from Midtrans
     */
    public function handleNotification(array $notification): array
    {
        try {
            $orderId = $notification['order_id'];
            $statusCode = $notification['status_code'];
            $grossAmount = $notification['gross_amount'];
            $transactionStatus = $notification['transaction_status'];
            $fraudStatus = $notification['fraud_status'] ?? null;

            Log::info('Received Midtrans notification', [
                'order_id' => $orderId,
                'transaction_status' => $transactionStatus,
                'status_code' => $statusCode
            ]);

            // Verify signature
            $signatureKey = hash('sha512', 
                $orderId . $statusCode . $grossAmount . config('midtrans.server_key')
            );

            if ($signatureKey !== $notification['signature_key']) {
                throw new Exception('Invalid signature key');
            }

            // Find transaction
            $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();
            
            if (!$transaction) {
                throw new Exception('Transaction not found: ' . $orderId);
            }

            // Update transaction based on status
            $this->updateTransactionStatus($transaction, $transactionStatus, $fraudStatus, $notification);

            return [
                'success' => true,
                'message' => 'Notification processed successfully'
            ];

        } catch (Exception $e) {
            Log::error('Failed to process Midtrans notification', [
                'error' => $e->getMessage(),
                'notification' => $notification
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update transaction status based on Midtrans response
     */
    private function updateTransactionStatus(Transaction $transaction, string $transactionStatus, ?string $fraudStatus, array $notification): void
    {
        $updateData = [
            'payment_gateway_status' => $transactionStatus,
            'payment_gateway_response' => $notification,
            'payment_gateway_transaction_id' => $notification['transaction_id'] ?? null,
        ];

        switch ($transactionStatus) {
            case 'capture':
                if ($fraudStatus === 'challenge') {
                    $updateData['status'] = 'pending';
                } else if ($fraudStatus === 'accept') {
                    $updateData['status'] = 'completed';
                    $updateData['payment_gateway_paid_at'] = now();
                    $updateData['amount_paid'] = $transaction->total_amount;
                    $updateData['change_amount'] = 0;

                    // Reduce stock when payment is confirmed (only if not already reduced)
                    if ($transaction->status !== 'completed') {
                        $this->reduceStockForTransaction($transaction);
                    }
                }
                break;

            case 'settlement':
                $updateData['status'] = 'completed';
                $updateData['payment_gateway_paid_at'] = now();
                $updateData['amount_paid'] = $transaction->total_amount;
                $updateData['change_amount'] = 0;

                // Reduce stock when payment is confirmed (only if not already reduced)
                if ($transaction->status !== 'completed') {
                    $this->reduceStockForTransaction($transaction);
                }
                break;

            case 'pending':
                $updateData['status'] = 'pending';
                break;

            case 'deny':
            case 'cancel':
                $updateData['status'] = 'cancelled';
                break;

            case 'expire':
                $updateData['status'] = 'expired';
                break;

            case 'failure':
                $updateData['status'] = 'failed';
                break;
        }

        $transaction->update($updateData);

        Log::info('Transaction status updated', [
            'transaction_id' => $transaction->id,
            'old_status' => $transaction->getOriginal('status'),
            'new_status' => $updateData['status'],
            'midtrans_status' => $transactionStatus
        ]);
    }

    /**
     * Check transaction status from Midtrans
     */
    public function checkTransactionStatus(string $orderId): array
    {
        try {
            Log::info('Checking transaction status from Midtrans', [
                'order_id' => $orderId,
                'server_key' => substr(config('midtrans.server_key'), 0, 10) . '...',
                'is_production' => config('midtrans.is_production'),
                'midtrans_config' => [
                    'merchant_id' => config('midtrans.merchant_id'),
                    'client_key' => substr(config('midtrans.client_key'), 0, 10) . '...',
                    'is_sanitized' => config('midtrans.is_sanitized'),
                    'is_3ds' => config('midtrans.is_3ds')
                ]
            ]);

            $status = MidtransTransaction::status($orderId);

            Log::info('Midtrans status response', [
                'order_id' => $orderId,
                'status' => $status
            ]);

            return [
                'success' => true,
                'data' => $status
            ];

        } catch (\Exception $e) {
            Log::error('Midtrans API error', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'error_class' => get_class($e)
            ]);

            // Provide more specific error messages
            $message = 'Gagal mengecek status pembayaran';
            $errorMessage = $e->getMessage();

            if (strpos($errorMessage, '404') !== false || strpos($errorMessage, 'not found') !== false) {
                $message = 'Transaksi tidak ditemukan di Midtrans. Mungkin transaksi sudah expired atau belum dibuat.';
            } elseif (strpos($errorMessage, '401') !== false || strpos($errorMessage, 'unauthorized') !== false) {
                $message = 'Konfigurasi Midtrans tidak valid. Periksa Server Key.';
            } elseif (strpos($errorMessage, 'timeout') !== false || strpos($errorMessage, 'connection') !== false) {
                $message = 'Koneksi ke Midtrans timeout. Silakan coba lagi.';
            } elseif (strpos($errorMessage, 'curl') !== false) {
                $message = 'Masalah koneksi internet. Periksa koneksi dan coba lagi.';
            }

            return [
                'success' => false,
                'message' => $message,
                'error_detail' => $errorMessage
            ];

        }
    }

    /**
     * Reduce stock for transaction items
     */
    private function reduceStockForTransaction(Transaction $transaction): void
    {
        Log::info('Starting stock reduction for transaction', [
            'transaction_id' => $transaction->id,
            'invoice_number' => $transaction->invoice_number,
            'current_status' => $transaction->status,
            'items_count' => $transaction->items->count()
        ]);

        foreach ($transaction->items as $item) {
            if ($item->processed_inventory_id) {
                $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                if ($product && $product->current_stock >= $item->quantity) {
                    $oldStock = $product->current_stock;
                    $product->decrement('current_stock', $item->quantity);
                    $product->refresh();

                    Log::info('Stock reduced for processed inventory', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity_reduced' => $item->quantity,
                        'old_stock' => $oldStock,
                        'new_stock' => $product->current_stock
                    ]);
                } else {
                    Log::warning('Insufficient stock for processed inventory', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id ?? 'not_found',
                        'required_quantity' => $item->quantity,
                        'available_stock' => $product->current_stock ?? 0
                    ]);
                }
            } elseif ($item->other_product_id) {
                $product = \App\Models\OtherProduct::find($item->other_product_id);
                if ($product && $product->current_stock >= $item->quantity) {
                    $oldStock = $product->current_stock;
                    $product->decrement('current_stock', $item->quantity);
                    $product->refresh();

                    Log::info('Stock reduced for other product', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity_reduced' => $item->quantity,
                        'old_stock' => $oldStock,
                        'new_stock' => $product->current_stock
                    ]);
                } else {
                    Log::warning('Insufficient stock for other product', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id ?? 'not_found',
                        'required_quantity' => $item->quantity,
                        'available_stock' => $product->current_stock ?? 0
                    ]);
                }
            }
        }

        Log::info('Stock reduction completed for transaction', [
            'transaction_id' => $transaction->id,
            'invoice_number' => $transaction->invoice_number
        ]);
    }
}
