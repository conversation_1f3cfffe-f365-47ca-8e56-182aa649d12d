<?php

namespace App\Services;

use App\Models\Transaction;
use Midtrans\Config;
use Midtrans\Snap;
use Midtrans\Transaction as MidtransTransaction;
use Illuminate\Support\Facades\Log;
use Exception;

class MidtransService
{
    public function __construct()
    {
        // Set Midtrans configuration
        Config::$serverKey = config('midtrans.server_key');
        Config::$isProduction = config('midtrans.is_production');
        Config::$isSanitized = config('midtrans.is_sanitized');
        Config::$is3ds = config('midtrans.is_3ds');
    }

    /**
     * Create Snap token for transaction
     */
    public function createSnapToken(Transaction $transaction): array
    {
        try {
            $params = $this->buildTransactionParams($transaction);
            
            Log::info('Creating Midtrans Snap token', [
                'transaction_id' => $transaction->id,
                'order_id' => $params['transaction_details']['order_id'],
                'gross_amount' => $params['transaction_details']['gross_amount']
            ]);

            $snapToken = Snap::getSnapToken($params);
            
            // Update transaction with snap token
            $transaction->update([
                'payment_gateway' => 'midtrans',
                'payment_gateway_order_id' => $params['transaction_details']['order_id'],
                'snap_token' => $snapToken,
                'payment_gateway_expired_at' => now()->addHours(config('midtrans.transaction_timeout', 24)),
                'status' => 'pending'
            ]);

            return [
                'success' => true,
                'snap_token' => $snapToken,
                'redirect_url' => $this->getSnapRedirectUrl($snapToken)
            ];

        } catch (Exception $e) {
            Log::error('Failed to create Midtrans Snap token', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Gagal membuat token pembayaran: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build transaction parameters for Midtrans
     */
    private function buildTransactionParams(Transaction $transaction): array
    {
        $orderId = 'UBI-' . $transaction->id . '-' . time();
        
        // Build item details
        $itemDetails = [];
        foreach ($transaction->items as $item) {
            // Get product name safely
            $productName = $item->product_name;
            if (empty($productName)) {
                if ($item->processed_inventory_id && $item->processedInventory) {
                    $productName = $item->processedInventory->name;
                } elseif ($item->other_product_id && $item->otherProduct) {
                    $productName = $item->otherProduct->name;
                } else {
                    $productName = 'Produk #' . ($item->processed_inventory_id ?? $item->other_product_id);
                }
            }

            $itemDetails[] = [
                'id' => $item->processed_inventory_id ?? $item->other_product_id ?? $item->id,
                'price' => (int) $item->price,
                'quantity' => $item->quantity,
                'name' => $productName,
                'category' => 'Food'
            ];
        }

        // Add tax if any
        if ($transaction->tax > 0) {
            $itemDetails[] = [
                'id' => 'TAX',
                'price' => (int) $transaction->tax,
                'quantity' => 1,
                'name' => 'Pajak'
            ];
        }

        // Subtract discount if any
        if ($transaction->discount > 0) {
            $itemDetails[] = [
                'id' => 'DISCOUNT',
                'price' => -(int) $transaction->discount,
                'quantity' => 1,
                'name' => 'Diskon'
            ];
        }

        $params = [
            'transaction_details' => [
                'order_id' => $orderId,
                'gross_amount' => (int) $transaction->total_amount,
            ],
            'item_details' => $itemDetails,
            'customer_details' => [
                'first_name' => $transaction->customer_name ?? 'Customer',
                'phone' => $transaction->customer_phone ?? '',
            ],
            'enabled_payments' => config('midtrans.enabled_payments'),
            'callbacks' => [
                'finish' => config('midtrans.finish_url'),
                'unfinish' => config('midtrans.unfinish_url'),
                'error' => config('midtrans.error_url'),
            ],
            'expiry' => [
                'start_time' => date('Y-m-d H:i:s O'),
                'unit' => 'hours',
                'duration' => config('midtrans.transaction_timeout', 24)
            ]
        ];

        return $params;
    }

    /**
     * Get Snap redirect URL
     */
    private function getSnapRedirectUrl(string $snapToken): string
    {
        $baseUrl = config('midtrans.is_production') 
            ? 'https://app.midtrans.com/snap/v2/vtweb/'
            : 'https://app.sandbox.midtrans.com/snap/v2/vtweb/';
            
        return $baseUrl . $snapToken;
    }

    /**
     * Handle payment notification from Midtrans
     */
    public function handleNotification(array $notification): array
    {
        try {
            $orderId = $notification['order_id'];
            $statusCode = $notification['status_code'];
            $grossAmount = $notification['gross_amount'];
            $transactionStatus = $notification['transaction_status'];
            $fraudStatus = $notification['fraud_status'] ?? null;

            Log::info('Received Midtrans notification', [
                'order_id' => $orderId,
                'transaction_status' => $transactionStatus,
                'status_code' => $statusCode
            ]);

            // Verify signature
            $signatureKey = hash('sha512', 
                $orderId . $statusCode . $grossAmount . config('midtrans.server_key')
            );

            if ($signatureKey !== $notification['signature_key']) {
                throw new Exception('Invalid signature key');
            }

            // Find transaction
            $transaction = Transaction::where('payment_gateway_order_id', $orderId)->first();
            
            if (!$transaction) {
                throw new Exception('Transaction not found: ' . $orderId);
            }

            // Update transaction based on status
            $this->updateTransactionStatus($transaction, $transactionStatus, $fraudStatus, $notification);

            return [
                'success' => true,
                'message' => 'Notification processed successfully'
            ];

        } catch (Exception $e) {
            Log::error('Failed to process Midtrans notification', [
                'error' => $e->getMessage(),
                'notification' => $notification
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Update transaction status based on Midtrans response
     */
    private function updateTransactionStatus(Transaction $transaction, string $transactionStatus, ?string $fraudStatus, array $notification): void
    {
        $updateData = [
            'payment_gateway_status' => $transactionStatus,
            'payment_gateway_response' => $notification,
            'payment_gateway_transaction_id' => $notification['transaction_id'] ?? null,
        ];

        switch ($transactionStatus) {
            case 'capture':
                if ($fraudStatus === 'challenge') {
                    $updateData['status'] = 'pending';
                } else if ($fraudStatus === 'accept') {
                    $updateData['status'] = 'completed';
                    $updateData['payment_gateway_paid_at'] = now();
                    $updateData['amount_paid'] = $transaction->total_amount;
                    $updateData['change_amount'] = 0;

                    // Reduce stock when payment is confirmed (only if not already reduced)
                    if ($transaction->status !== 'completed') {
                        $this->reduceStockForTransaction($transaction);
                    }
                }
                break;

            case 'settlement':
                $updateData['status'] = 'completed';
                $updateData['payment_gateway_paid_at'] = now();
                $updateData['amount_paid'] = $transaction->total_amount;
                $updateData['change_amount'] = 0;

                // Reduce stock when payment is confirmed (only if not already reduced)
                if ($transaction->status !== 'completed') {
                    $this->reduceStockForTransaction($transaction);
                }
                break;

            case 'pending':
                $updateData['status'] = 'pending';
                break;

            case 'deny':
            case 'cancel':
                $updateData['status'] = 'cancelled';
                break;

            case 'expire':
                $updateData['status'] = 'expired';
                break;

            case 'failure':
                $updateData['status'] = 'failed';
                break;
        }

        $transaction->update($updateData);

        Log::info('Transaction status updated', [
            'transaction_id' => $transaction->id,
            'old_status' => $transaction->getOriginal('status'),
            'new_status' => $updateData['status'],
            'midtrans_status' => $transactionStatus
        ]);
    }

    /**
     * Check transaction status from Midtrans
     */
    public function checkTransactionStatus(string $orderId): array
    {
        try {
            $status = MidtransTransaction::status($orderId);
            
            return [
                'success' => true,
                'data' => $status
            ];

        } catch (Exception $e) {
            Log::error('Failed to check transaction status', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Reduce stock for transaction items
     */
    private function reduceStockForTransaction(Transaction $transaction): void
    {
        Log::info('Starting stock reduction for transaction', [
            'transaction_id' => $transaction->id,
            'invoice_number' => $transaction->invoice_number,
            'current_status' => $transaction->status,
            'items_count' => $transaction->items->count()
        ]);

        foreach ($transaction->items as $item) {
            if ($item->processed_inventory_id) {
                $product = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                if ($product && $product->current_stock >= $item->quantity) {
                    $oldStock = $product->current_stock;
                    $product->decrement('current_stock', $item->quantity);
                    $product->refresh();

                    Log::info('Stock reduced for processed inventory', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity_reduced' => $item->quantity,
                        'old_stock' => $oldStock,
                        'new_stock' => $product->current_stock
                    ]);
                } else {
                    Log::warning('Insufficient stock for processed inventory', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id ?? 'not_found',
                        'required_quantity' => $item->quantity,
                        'available_stock' => $product->current_stock ?? 0
                    ]);
                }
            } elseif ($item->other_product_id) {
                $product = \App\Models\OtherProduct::find($item->other_product_id);
                if ($product && $product->current_stock >= $item->quantity) {
                    $oldStock = $product->current_stock;
                    $product->decrement('current_stock', $item->quantity);
                    $product->refresh();

                    Log::info('Stock reduced for other product', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'quantity_reduced' => $item->quantity,
                        'old_stock' => $oldStock,
                        'new_stock' => $product->current_stock
                    ]);
                } else {
                    Log::warning('Insufficient stock for other product', [
                        'transaction_id' => $transaction->id,
                        'product_id' => $product->id ?? 'not_found',
                        'required_quantity' => $item->quantity,
                        'available_stock' => $product->current_stock ?? 0
                    ]);
                }
            }
        }

        Log::info('Stock reduction completed for transaction', [
            'transaction_id' => $transaction->id,
            'invoice_number' => $transaction->invoice_number
        ]);
    }
}
