<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\MidtransService;
use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Models\User;

class TestPaymentGateway extends Command
{
    protected $signature = 'payment:test';
    protected $description = 'Test payment gateway functionality';

    public function handle()
    {
        $this->info('🚀 Testing Payment Gateway Midtrans...');
        $this->newLine();

        // Test 1: Configuration
        $this->info('1. Testing Configuration...');
        $this->line('   Merchant ID: ' . config('midtrans.merchant_id'));
        $this->line('   Client Key: ' . substr(config('midtrans.client_key'), 0, 20) . '...');
        $this->line('   Server Key: ' . substr(config('midtrans.server_key'), 0, 20) . '...');
        $this->line('   Environment: ' . (config('midtrans.is_production') ? 'Production' : 'Sandbox'));
        $this->newLine();

        // Test 2: Create Test Transaction
        $this->info('2. Creating Test Transaction...');
        
        try {
            // Get first user (admin)
            $user = User::first();
            if (!$user) {
                $this->error('   ❌ No users found. Please create a user first.');
                return;
            }

            // Get first product
            $product = ProcessedInventory::where('current_stock', '>', 0)->first();
            if (!$product) {
                $this->error('   ❌ No products with stock found. Please add products first.');
                return;
            }

            // Create test transaction
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'customer_name' => 'Test Customer',
                'customer_phone' => '081234567890',
                'subtotal' => 50000,
                'tax' => 0,
                'discount' => 0,
                'total_amount' => 50000,
                'amount_paid' => 0,
                'change_amount' => 0,
                'payment_method' => 'gateway',
                'status' => 'pending',
                'notes' => 'Test transaction for payment gateway - ' . now()->format('H:i:s')
            ]);

            // Create transaction item
            TransactionItem::create([
                'transaction_id' => $transaction->id,
                'processed_inventory_id' => $product->id,
                'product_name' => $product->name,
                'price' => 25000,
                'quantity' => 2,
                'subtotal' => 50000
            ]);

            $this->line('   ✅ Test transaction created: #' . $transaction->invoice_number);
            $this->newLine();

            // Test 3: Create Snap Token
            $this->info('3. Testing Snap Token Creation...');
            
            $midtransService = new MidtransService();
            $result = $midtransService->createSnapToken($transaction);

            if ($result['success']) {
                $this->line('   ✅ Snap token created successfully');
                $this->line('   📱 Snap Token: ' . substr($result['snap_token'], 0, 30) . '...');
                $this->line('   🔗 Payment URL: ' . $result['redirect_url']);
                $this->newLine();

                // Test 4: Display Test Information
                $this->info('4. Test Payment Information:');
                $this->line('   🆔 Transaction ID: ' . $transaction->id);
                $this->line('   📄 Invoice: ' . $transaction->invoice_number);
                $this->line('   💰 Amount: Rp ' . number_format($transaction->total_amount, 0, ',', '.'));
                $this->line('   🔗 Payment URL: ' . $result['redirect_url']);
                $this->newLine();

                $this->info('5. Test Cards for Sandbox:');
                $this->line('   ✅ Success: 4811 1111 1111 1114 (CVV: 123, Exp: 01/25)');
                $this->line('   ❌ Failed:  4911 1111 1111 1113 (CVV: 123, Exp: 01/25)');
                $this->line('   ⏳ Pending: 4411 1111 1111 1118 (CVV: 123, Exp: 01/25)');
                $this->newLine();

                $this->info('6. Next Steps:');
                $this->line('   1. Open the payment URL in browser');
                $this->line('   2. Complete payment using test cards');
                $this->line('   3. Check transaction status in admin panel');
                $this->line('   4. Verify webhook notifications');
                $this->newLine();

                $this->info('✅ Payment Gateway Test Completed Successfully!');

            } else {
                $this->error('   ❌ Failed to create snap token: ' . $result['message']);
                
                // Clean up test transaction
                $transaction->items()->delete();
                $transaction->delete();
            }

        } catch (\Exception $e) {
            $this->error('   ❌ Error: ' . $e->getMessage());
            
            // Clean up if transaction was created
            if (isset($transaction)) {
                $transaction->items()->delete();
                $transaction->delete();
            }
        }
    }
}
