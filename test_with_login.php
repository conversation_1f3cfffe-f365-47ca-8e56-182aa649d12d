<?php

echo "🧪 TESTING PAYMENT GATEWAY WITH LOGIN\n";
echo "=====================================\n\n";

$baseUrl = 'http://127.0.0.1:8000';
$cookieFile = 'login_cookies.txt';

// Step 1: Get login page and CSRF token
echo "1. Getting login page...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);

$loginPageResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    echo "   ❌ Failed to get login page: HTTP {$httpCode}\n";
    exit(1);
}

// Extract CSRF token from login page
preg_match('/name="_token" value="([^"]+)"/', $loginPageResponse, $matches);
$loginToken = $matches[1] ?? '';

if (empty($loginToken)) {
    echo "   ❌ Login CSRF token not found\n";
    exit(1);
}

echo "   ✅ Login page loaded\n";
echo "   🔑 Login token: " . substr($loginToken, 0, 20) . "...\n\n";

// Step 2: Attempt login
echo "2. Attempting login...\n";

$loginData = [
    '_token' => $loginToken,
    'email' => '<EMAIL>', // Default admin email
    'password' => 'password' // Default password
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/login');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, $cookieFile);
curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($loginData));

$loginResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
curl_close($ch);

echo "   📄 Login response: HTTP {$httpCode}\n";
echo "   🌐 Final URL: {$finalUrl}\n";

if (strpos($finalUrl, '/login') !== false) {
    echo "   ❌ Login failed - still on login page\n";
    echo "   💡 Try different credentials or check if user exists\n\n";
    
    // Try to create admin user or use different approach
    echo "3. Trying alternative login methods...\n";
    echo "   💡 You may need to:\n";
    echo "      - Create admin user manually\n";
    echo "      - Check database for existing users\n";
    echo "      - Use correct email/password combination\n\n";
} else {
    echo "   ✅ Login successful!\n\n";
    
    // Step 3: Test POS page after login
    echo "3. Testing POS page after login...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/pos');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
    
    $posResponse = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200 && strpos($posResponse, 'csrf-token') !== false) {
        echo "   ✅ POS page accessible\n";
        
        // Extract CSRF token from POS page
        preg_match('/name="csrf-token" content="([^"]+)"/', $posResponse, $matches);
        $csrfToken = $matches[1] ?? '';
        
        if (!empty($csrfToken)) {
            echo "   ✅ CSRF token found: " . substr($csrfToken, 0, 20) . "...\n\n";
            
            // Step 4: Test payment gateway
            echo "4. Testing payment gateway...\n";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $baseUrl . '/payment/create/26');
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_COOKIEFILE, $cookieFile);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json',
                'X-CSRF-TOKEN: ' . $csrfToken,
                'X-Requested-With: XMLHttpRequest'
            ]);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
            
            echo "   🚀 Making payment request...\n";
            $startTime = microtime(true);
            
            $response = curl_exec($ch);
            $endTime = microtime(true);
            
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            $duration = round(($endTime - $startTime) * 1000, 2);
            
            if ($error) {
                echo "   ❌ cURL Error: {$error}\n";
            } else {
                echo "   ✅ HTTP {$httpCode} ({$duration}ms)\n";
                
                if ($duration > 30000) {
                    echo "   ⚠️  WARNING: Very slow response\n";
                } elseif ($duration > 10000) {
                    echo "   ⚠️  WARNING: Slow response\n";
                } else {
                    echo "   ✅ Good response time\n";
                }
                
                echo "   📄 Response: " . substr($response, 0, 300) . "...\n";
                
                $responseData = json_decode($response, true);
                if ($responseData) {
                    echo "   📊 JSON Response:\n";
                    echo "      Success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
                    echo "      Message: " . ($responseData['message'] ?? 'no message') . "\n";
                    
                    if (isset($responseData['redirect_url'])) {
                        echo "      ✅ Redirect URL: {$responseData['redirect_url']}\n";
                    }
                    
                    if (isset($responseData['error_detail'])) {
                        echo "      ❌ Error Detail: {$responseData['error_detail']}\n";
                    }
                }
            }
        } else {
            echo "   ❌ CSRF token not found in POS page\n";
        }
    } else {
        echo "   ❌ POS page not accessible: HTTP {$httpCode}\n";
    }
}

// Cleanup
@unlink($cookieFile);

echo "\n🏁 Test completed!\n\n";

echo "💡 SUMMARY:\n";
echo "===========\n";
echo "- If login failed: Check user credentials in database\n";
echo "- If POS accessible: Authentication is working\n";
echo "- If payment gateway works: Issue was authentication\n";
echo "- If still slow/hanging: Issue is in MidtransService\n\n";

echo "🔧 SOLUTIONS:\n";
echo "=============\n";
echo "1. Make sure user is logged in before using payment gateway\n";
echo "2. Add proper session management\n";
echo "3. Handle authentication errors gracefully\n";
echo "4. Add loading states and timeout handling\n\n";
