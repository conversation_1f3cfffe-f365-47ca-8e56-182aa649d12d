<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Services\MidtransService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    protected $midtransService;

    public function __construct(MidtransService $midtransService)
    {
        $this->midtransService = $midtransService;
        $this->middleware('auth');
    }

    /**
     * Create payment via Midtrans
     */
    public function createPayment(Request $request, Transaction $transaction)
    {
        try {
            Log::info('Creating payment for transaction', [
                'transaction_id' => $transaction->id,
                'user_id' => auth()->id(),
                'total_amount' => $transaction->total_amount,
                'method' => $request->method()
            ]);

            // Validate transaction
            if ($transaction->status !== 'pending') {
                $message = 'Transaksi sudah diproses sebelumnya';

                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => $message], 400);
                }

                return redirect()->route('transactions.show', $transaction)->with('error', $message);
            }

            if ($transaction->items->count() === 0) {
                $message = 'Transaksi tidak memiliki item';

                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => $message], 400);
                }

                return redirect()->route('transactions.show', $transaction)->with('error', $message);
            }

            // Create Snap token
            $result = $this->midtransService->createSnapToken($transaction);

            if ($result['success']) {
                Log::info('Payment gateway created successfully', [
                    'transaction_id' => $transaction->id,
                    'redirect_url' => $result['redirect_url']
                ]);

                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => true,
                        'message' => 'Token pembayaran berhasil dibuat',
                        'snap_token' => $result['snap_token'],
                        'redirect_url' => $result['redirect_url']
                    ]);
                }

                // For GET request, redirect directly to Midtrans
                return redirect($result['redirect_url']);

            } else {
                $message = $result['message'];

                if ($request->expectsJson()) {
                    return response()->json(['success' => false, 'message' => $message], 500);
                }

                return redirect()->route('transactions.show', $transaction)->with('error', $message);
            }

        } catch (\Exception $e) {
            Log::error('Payment creation failed', [
                'transaction_id' => $transaction->id,
                'error' => $e->getMessage()
            ]);

            $message = 'Gagal membuat pembayaran. Silakan coba lagi.';

            if ($request->expectsJson()) {
                return response()->json(['success' => false, 'message' => $message], 500);
            }

            return redirect()->route('transactions.show', $transaction)->with('error', $message);
        }
    }

    /**
     * Handle Midtrans notification
     */
    public function handleNotification(Request $request)
    {
        try {
            $notification = $request->all();
            
            Log::info('Received Midtrans notification', $notification);
            
            $result = $this->midtransService->handleNotification($notification);
            
            if ($result) {
                return response('OK', 200);
            } else {
                return response('Failed', 400);
            }
            
        } catch (\Exception $e) {
            Log::error('Notification handling failed', [
                'error' => $e->getMessage(),
                'notification' => $request->all()
            ]);
            
            return response('Error', 500);
        }
    }

    /**
     * Payment finish page
     */
    public function paymentFinish(Request $request)
    {
        $orderId = $request->get('order_id');
        $statusCode = $request->get('status_code');
        $transactionStatus = $request->get('transaction_status');

        Log::info('Payment finish', [
            'order_id' => $orderId,
            'status_code' => $statusCode,
            'transaction_status' => $transactionStatus
        ]);

        return view('payment.finish', [
            'order_id' => $orderId,
            'status' => $transactionStatus
        ]);
    }

    /**
     * Payment unfinish page
     */
    public function paymentUnfinish(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::info('Payment unfinish', ['order_id' => $orderId]);
        
        return view('payment.unfinish', ['order_id' => $orderId]);
    }

    /**
     * Payment error page
     */
    public function paymentError(Request $request)
    {
        $orderId = $request->get('order_id');
        
        Log::info('Payment error', ['order_id' => $orderId]);
        
        return view('payment.error', ['order_id' => $orderId]);
    }

    /**
     * Check payment status
     */
    public function checkStatus(Transaction $transaction)
    {
        try {
            return response()->json([
                'success' => true,
                'transaction' => [
                    'id' => $transaction->id,
                    'status' => $transaction->status,
                    'payment_status' => $transaction->payment_status ?? 'pending',
                    'total_amount' => $transaction->total_amount
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Gagal mengecek status pembayaran'
            ], 500);
        }
    }
}
