<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class TransactionController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Filter berdasarkan tanggal jika parameter disediakan
        $query = Transaction::with('items.product', 'user')
            ->withCount('items as items_count');

        // Filter berdasarkan tanggal
        if ($request->has('start_date') && $request->start_date) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }

        if ($request->has('end_date') && $request->end_date) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Filter berdasarkan pencarian jika ada
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'like', "%{$search}%")
                  ->orWhere('customer_info', 'like', "%{$search}%")
                  ->orWhere('total_amount', 'like', "%{$search}%")
                  ->orWhereHas('user', function($u) use ($search) {
                      $u->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Dapatkan transaksi dengan paginasi
        $transactions = $query->latest()->paginate(15);

        // Hitung ringkasan transaksi untuk tampilan
        $transactionQuery = clone $query;
        $totalTransactions = $transactionQuery->count();
        $totalAmount = $transactionQuery->sum('total_amount');
        $averageAmount = $totalTransactions > 0 ? $totalAmount / $totalTransactions : 0;

        $transactionSummary = [
            'count' => $totalTransactions,
            'total' => $totalAmount,
            'average' => $averageAmount
        ];

        return view('transactions.index', compact('transactions', 'transactionSummary'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Get active processed inventory products with stock
        $processedProducts = ProcessedInventory::where('current_stock', '>', 0)
                                      ->where('is_active', true)
                                      ->orderBy('name')
                                      ->get();

        // Get active other products with stock
        $otherProducts = OtherProduct::where('current_stock', '>', 0)
                                     ->where('is_active', true)
                                     ->orderBy('name')
                                     ->get();

        // Combine both product types
        $products = $processedProducts->concat($otherProducts);

        // Get available categories (using a simple grouping since we don't have a categories table)
        $productTypes = ProcessedInventory::select('product_type')
                                        ->distinct()
                                        ->whereNotNull('product_type')
                                        ->get()
                                        ->pluck('product_type');

        // Add other product categories
        $otherProductCategories = OtherProduct::select('category')
                                           ->distinct()
                                           ->whereNotNull('category')
                                           ->get()
                                           ->pluck('category');

        $combinedCategories = $productTypes->concat($otherProductCategories)->unique();

        $categories = collect($combinedCategories)->map(function($type) {
            return (object)[
                'id' => strtolower(str_replace(' ', '_', $type)),
                'name' => $type
            ];
        });

        return view('transactions.pos', compact('products', 'categories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'items' => 'required',
            'payment_method' => 'required|in:cash,transfer,qris,debit,credit',
            'amount_paid' => 'required|numeric|min:0',
            'notes' => 'nullable|string|max:1000',
        ]);

        DB::beginTransaction();
        try {
            // Parse items from JSON if it's a string
            $items = $request->items;
            if (is_string($items)) {
                $items = json_decode($items, true);
            }

            if (empty($items)) {
                throw new \Exception("Tidak ada item dalam keranjang");
            }

            // Calculate subtotal and total first
            $subtotal = 0;
            $totalItems = [];

            // Verify each item and calculate subtotal
            foreach ($items as $item) {
                // Check if the item is from ProcessedInventory or OtherProduct
                $product = null;
                $productType = $item['type'] ?? 'processed'; // Default to processed if not specified

                if ($productType === 'other') {
                    $product = OtherProduct::findOrFail($item['id']);
                } else {
                    $product = ProcessedInventory::findOrFail($item['id']);
                }

                // Check stock availability
                if ($product->current_stock < $item['quantity']) {
                    throw new \Exception("Stok tidak mencukupi untuk {$product->name}. Tersedia: {$product->current_stock}");
                }

                // Calculate subtotal for this item
                $itemSubtotal = $product->selling_price * $item['quantity'];
                $subtotal += $itemSubtotal;

                // Save item details for later
                $totalItems[] = [
                    'product' => $product,
                    'product_type' => $productType,
                    'quantity' => $item['quantity'],
                    'price' => $product->selling_price,
                    'subtotal' => $itemSubtotal
                ];
            }

            // Calculate total (subtotal + tax - discount)
            $taxRate = 0; // Bisa disesuaikan jika ada pajak
            $tax = $subtotal * $taxRate;
            $discount = 0; // Bisa disesuaikan jika ada diskon
            $totalAmount = $subtotal + $tax - $discount;

            // Check if amount paid is sufficient
            if ($request->amount_paid < $totalAmount) {
                throw new \Exception("Pembayaran tidak mencukupi. Total: Rp ".number_format($totalAmount, 0, ',', '.'));
            }

            // Calculate change
            $changeAmount = $request->amount_paid - $totalAmount;

            // Parse customer info (name and phone)
            $customerName = $request->customer_name ?? null;
            $customerPhone = $request->customer_phone ?? null;

            // Create transaction
            $transaction = Transaction::create([
                'user_id' => Auth::id(),
                'customer_name' => $customerName,
                'customer_phone' => $customerPhone,
                'subtotal' => $subtotal,
                'tax' => $tax,
                'discount' => $discount,
                'total_amount' => $totalAmount,
                'amount_paid' => $request->amount_paid,
                'change_amount' => $changeAmount,
                'payment_method' => $request->payment_method,
                'status' => 'completed',
                'notes' => $request->notes,
            ]);

            // Create transaction items and reduce inventory
            foreach ($totalItems as $item) {
                $productId = $item['product']->id;
                $processedInventoryId = null;

                // Set the appropriate field based on product type
                if ($item['product_type'] === 'processed') {
                    $processedInventoryId = $productId;
                }

                // Create transaction item
                TransactionItem::create([
                    'transaction_id' => $transaction->id,
                    'processed_inventory_id' => $processedInventoryId,
                    'product_id' => $productId,
                    'product_name' => $item['product']->name,
                    'quantity' => $item['quantity'],
                    'price' => $item['price'],
                    'subtotal' => $item['subtotal'],
                ]);

                // Reduce inventory
                $item['product']->decrement('current_stock', $item['quantity']);
            }

            DB::commit();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Transaksi berhasil dibuat',
                    'transaction' => $transaction,
                ]);
            }

            // Add logging for debugging
            \Log::info('Transaction created successfully', [
                'transaction_id' => $transaction->id,
                'invoice_number' => $transaction->invoice_number,
                'redirect_to' => route('transactions.show', $transaction)
            ]);

            return redirect()->route('transactions.show', $transaction)
                            ->with('success', 'Transaksi berhasil dibuat');

        } catch (\Exception $e) {
            DB::rollback();

            if ($request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 422);
            }

            return redirect()->back()
                            ->with('error', $e->getMessage())
                            ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Transaction $transaction)
    {
        $transaction->load('items.product', 'user');
        return view('transactions.show', compact('transaction'));
    }

    /**
     * Cancel a transaction (soft delete)
     */
    public function cancel(Transaction $transaction)
    {
        // Only allow cancellation within 24 hours
        if (now()->diffInHours($transaction->created_at) > 24) {
            return redirect()->back()
                            ->with('error', 'Transaksi tidak dapat dibatalkan setelah 24 jam');
        }

        DB::beginTransaction();
        try {
            // Return items to inventory
            foreach ($transaction->items as $item) {
                $item->product->increment('current_stock', $item->quantity);
            }

            // Update transaction status
            $transaction->update(['status' => 'cancelled']);

            DB::commit();

            return redirect()->route('transactions.index')
                            ->with('success', 'Transaksi berhasil dibatalkan');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()
                            ->with('error', 'Gagal membatalkan transaksi: ' . $e->getMessage());
        }
    }

    /**
     * Generate a receipt for the transaction
     */
    public function receipt(Transaction $transaction)
    {
        $transaction->load('items.product', 'user');
        return view('transactions.receipt', compact('transaction'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Transaction $transaction)
    {
        DB::beginTransaction();
        try {
            // Kembalikan stok produk
            foreach ($transaction->items as $item) {
                if ($item->processed_inventory_id) {
                    // Jika item adalah processed inventory
                    $product = ProcessedInventory::find($item->processed_inventory_id);
                    if ($product) {
                        $product->increment('current_stock', $item->quantity);
                    }
                } elseif ($item->product_id) {
                    // Jika item adalah other product
                    $product = OtherProduct::find($item->product_id);
                    if ($product) {
                        $product->increment('current_stock', $item->quantity);
                    }
                }
            }

            // Hapus transaksi (soft delete)
            $transaction->delete();

            DB::commit();

            return redirect()->route('transactions.index')
                ->with('success', 'Transaksi berhasil dihapus dan stok produk telah dikembalikan.');
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()
                ->with('error', 'Gagal menghapus transaksi: ' . $e->getMessage());
        }
    }

    /**
     * Get daily sales report
     */
    public function dailyReport(Request $request)
    {
        $date = $request->get('date', today()->format('Y-m-d'));

        $transactions = Transaction::whereDate('created_at', $date)
                                   ->where('status', 'completed')
                                   ->with('items.product', 'user')
                                   ->latest()
                                   ->get();

        $totalSales = $transactions->sum('total_amount');
        $totalTransactions = $transactions->count();
        $averageTransaction = $totalTransactions > 0 ? $totalSales / $totalTransactions : 0;

        // Group by payment method
        $paymentSummary = $transactions->groupBy('payment_method')
                                       ->map(function ($group) {
                                           return [
                                               'count' => $group->count(),
                                               'total' => $group->sum('total_amount'),
                                           ];
                                       });

        // Top selling products
        $topProducts = TransactionItem::whereHas('transaction', function ($query) use ($date) {
                                        $query->whereDate('created_at', $date)
                                              ->where('status', 'completed');
                                     })
                                     ->with('product')
                                     ->select('product_id', DB::raw('SUM(quantity) as total_qty'))
                                     ->groupBy('product_id')
                                     ->orderByDesc('total_qty')
                                     ->limit(5)
                                     ->get();

        return view('transactions.daily-report', compact(
            'transactions',
            'totalSales',
            'totalTransactions',
            'averageTransaction',
            'paymentSummary',
            'topProducts',
            'date'
        ));
    }
}
