<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔍 DEBUGGING TRANSACTION ITEMS ERROR\n";
echo "===================================\n\n";

// Check recent transactions that failed
echo "1. Checking recent failed transactions...\n";

$failedTransactionIds = [44, 45]; // From the logs

foreach ($failedTransactionIds as $transactionId) {
    echo "\n   📋 Transaction ID: {$transactionId}\n";
    
    try {
        $transaction = \App\Models\Transaction::with('items')->find($transactionId);
        
        if (!$transaction) {
            echo "      ❌ Transaction not found\n";
            continue;
        }
        
        echo "      ✅ Transaction found\n";
        echo "      📊 Total amount: {$transaction->total_amount}\n";
        echo "      📊 Items count: " . $transaction->items->count() . "\n";
        echo "      📊 Customer: {$transaction->customer_name}\n";
        echo "      📊 Status: {$transaction->status}\n";
        
        echo "      📋 Items details:\n";
        foreach ($transaction->items as $index => $item) {
            echo "         Item #{$index}:\n";
            echo "            ID: {$item->id}\n";
            echo "            Product Name: {$item->product_name}\n";
            echo "            Price: {$item->price}\n";
            echo "            Quantity: {$item->quantity}\n";
            echo "            Processed ID: {$item->processed_inventory_id}\n";
            echo "            Other Product ID: {$item->product_id}\n";
            
            // Check if referenced products exist
            if ($item->processed_inventory_id) {
                $processedProduct = \App\Models\ProcessedInventory::find($item->processed_inventory_id);
                if ($processedProduct) {
                    echo "            ✅ ProcessedInventory exists: {$processedProduct->name}\n";
                } else {
                    echo "            ❌ ProcessedInventory NOT FOUND: {$item->processed_inventory_id}\n";
                }
            }
            
            if ($item->product_id) {
                $otherProduct = \App\Models\OtherProduct::find($item->product_id);
                if ($otherProduct) {
                    echo "            ✅ OtherProduct exists: {$otherProduct->name}\n";
                } else {
                    echo "            ❌ OtherProduct NOT FOUND: {$item->product_id}\n";
                }
            }
            echo "\n";
        }
        
    } catch (\Exception $e) {
        echo "      ❌ Error checking transaction: " . $e->getMessage() . "\n";
    }
}

echo "\n";

// Check for products with ID 10023
echo "2. Checking for product ID 10023...\n";

try {
    $processedProduct = \App\Models\ProcessedInventory::find(10023);
    if ($processedProduct) {
        echo "   ✅ ProcessedInventory ID 10023 found: {$processedProduct->name}\n";
    } else {
        echo "   ❌ ProcessedInventory ID 10023 NOT FOUND\n";
    }
    
    $otherProduct = \App\Models\OtherProduct::find(10023);
    if ($otherProduct) {
        echo "   ✅ OtherProduct ID 10023 found: {$otherProduct->name}\n";
    } else {
        echo "   ❌ OtherProduct ID 10023 NOT FOUND\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking product 10023: " . $e->getMessage() . "\n";
}

echo "\n";

// Check all products to see ID ranges
echo "3. Checking product ID ranges...\n";

try {
    $processedIds = \App\Models\ProcessedInventory::pluck('id')->toArray();
    $otherIds = \App\Models\OtherProduct::pluck('id')->toArray();
    
    echo "   📊 ProcessedInventory IDs: " . implode(', ', $processedIds) . "\n";
    echo "   📊 OtherProduct IDs: " . implode(', ', $otherIds) . "\n";
    
    $allIds = array_merge($processedIds, $otherIds);
    echo "   📊 All product IDs: " . implode(', ', $allIds) . "\n";
    
    if (in_array(10023, $allIds)) {
        echo "   ✅ ID 10023 exists in products\n";
    } else {
        echo "   ❌ ID 10023 does NOT exist in any products\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking ID ranges: " . $e->getMessage() . "\n";
}

echo "\n";

// Check transaction items that might reference 10023
echo "4. Checking transaction items referencing 10023...\n";

try {
    $itemsWithProcessed10023 = \App\Models\TransactionItem::where('processed_inventory_id', 10023)->get();
    $itemsWithOther10023 = \App\Models\TransactionItem::where('product_id', 10023)->get();
    
    echo "   📊 Transaction items with processed_inventory_id = 10023: " . $itemsWithProcessed10023->count() . "\n";
    echo "   📊 Transaction items with product_id = 10023: " . $itemsWithOther10023->count() . "\n";
    
    foreach ($itemsWithProcessed10023 as $item) {
        echo "      - Transaction Item ID: {$item->id}, Transaction ID: {$item->transaction_id}\n";
    }
    
    foreach ($itemsWithOther10023 as $item) {
        echo "      - Transaction Item ID: {$item->id}, Transaction ID: {$item->transaction_id}\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking items with 10023: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🏁 Debug completed!\n\n";

echo "💡 ANALYSIS:\n";
echo "============\n";
echo "The error 'Undefined array key 10023' likely occurs when:\n";
echo "1. Transaction items reference product ID 10023 that doesn't exist\n";
echo "2. MidtransService tries to access product data using this ID\n";
echo "3. The product was deleted but transaction items still reference it\n\n";

echo "🔧 SOLUTION:\n";
echo "============\n";
echo "1. Clean up transaction items with invalid product references\n";
echo "2. Add validation in MidtransService before accessing product data\n";
echo "3. Handle missing products gracefully\n";
echo "4. Prevent creation of transaction items with invalid product IDs\n\n";
