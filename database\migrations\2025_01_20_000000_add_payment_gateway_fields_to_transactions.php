<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Payment Gateway Fields
            $table->string('payment_gateway')->nullable()->after('payment_method');
            $table->string('payment_gateway_transaction_id')->nullable()->after('payment_gateway');
            $table->string('payment_gateway_order_id')->nullable()->after('payment_gateway_transaction_id');
            $table->string('payment_gateway_status')->nullable()->after('payment_gateway_order_id');
            $table->text('payment_gateway_response')->nullable()->after('payment_gateway_status');
            $table->string('snap_token')->nullable()->after('payment_gateway_response');
            $table->string('snap_redirect_url')->nullable()->after('snap_token');
            $table->timestamp('payment_gateway_paid_at')->nullable()->after('snap_redirect_url');
            $table->timestamp('payment_gateway_expired_at')->nullable()->after('payment_gateway_paid_at');
            
            // Update status enum to include pending
            $table->dropColumn('status');
        });

        // Add new status column with updated enum
        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('status', ['pending', 'completed', 'cancelled', 'refunded', 'failed', 'expired'])
                  ->default('pending')
                  ->after('notes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn([
                'payment_gateway',
                'payment_gateway_transaction_id',
                'payment_gateway_order_id',
                'payment_gateway_status',
                'payment_gateway_response',
                'snap_token',
                'snap_redirect_url',
                'payment_gateway_paid_at',
                'payment_gateway_expired_at'
            ]);
            
            $table->dropColumn('status');
        });

        // Restore original status column
        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('status', ['completed', 'cancelled', 'refunded'])
                  ->default('completed')
                  ->after('notes');
        });
    }
};
