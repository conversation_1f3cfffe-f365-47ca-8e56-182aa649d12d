<?php $__env->startSection('title', '<PERSON><PERSON><PERSON> Stok Menipis'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
            Kelola Stok Menipis
        </h1>
        <div>
            <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
            </a>
        </div>
    </div>

    <!-- Alert Summary -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Ubi Mentah
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($lowStockRaw->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-seedling fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Ubi Olahan
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($lowStockProcessed->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-fire fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Produk Lain
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($lowStockOther->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Ubi Mentah Stok Menipis -->
    <?php if($lowStockRaw->count() > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-danger">
                <i class="fas fa-seedling me-2"></i>Ubi Mentah - Stok Menipis
            </h6>
            <a href="<?php echo e(route('raw-inventory.index')); ?>" class="btn btn-danger btn-sm">
                <i class="fas fa-plus"></i> Tambah Stok
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Jenis Ubi</th>
                            <th>Stok Saat Ini</th>
                            <th>Batas Minimum</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $lowStockRaw; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($item->type); ?></td>
                            <td>
                                <span class="badge badge-danger"><?php echo e($item->current_stock); ?> kg</span>
                            </td>
                            <td>10 kg</td>
                            <td>
                                <span class="badge badge-danger">
                                    <i class="fas fa-exclamation-triangle"></i> Kritis
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo e(route('raw-inventory.show', $item->id)); ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> Detail
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Ubi Olahan Stok Menipis -->
    <?php if($lowStockProcessed->count() > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-warning">
                <i class="fas fa-fire me-2"></i>Ubi Olahan - Stok Menipis
            </h6>
            <a href="<?php echo e(route('processed-inventory.index')); ?>" class="btn btn-warning btn-sm">
                <i class="fas fa-cogs"></i> Proses Ubi
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Nama Produk</th>
                            <th>Batch</th>
                            <th>Stok Saat Ini</th>
                            <th>Batas Minimum</th>
                            <th>Tanggal Kadaluarsa</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $lowStockProcessed; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($item->name); ?></td>
                            <td><?php echo e($item->batch_number); ?></td>
                            <td>
                                <span class="badge badge-warning"><?php echo e($item->current_stock); ?> pcs</span>
                            </td>
                            <td><?php echo e($item->min_stock_threshold ?? 5); ?> pcs</td>
                            <td><?php echo e(\Carbon\Carbon::parse($item->expiry_date)->format('d/m/Y')); ?></td>
                            <td>
                                <?php if(\Carbon\Carbon::parse($item->expiry_date)->diffInDays(now()) <= 3): ?>
                                    <span class="badge badge-danger">
                                        <i class="fas fa-clock"></i> Segera Kadaluarsa
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-warning">
                                        <i class="fas fa-exclamation-triangle"></i> Stok Rendah
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?php echo e(route('processed-inventory.show', $item->id)); ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> Detail
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Produk Lain Stok Menipis -->
    <?php if($lowStockOther->count() > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-info">
                <i class="fas fa-box me-2"></i>Produk Lain - Stok Menipis
            </h6>
            <a href="<?php echo e(route('other-products.index')); ?>" class="btn btn-info btn-sm">
                <i class="fas fa-plus"></i> Kelola Produk
            </a>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Nama Produk</th>
                            <th>SKU</th>
                            <th>Stok Saat Ini</th>
                            <th>Batas Minimum</th>
                            <th>Kategori</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $lowStockOther; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($item->name); ?></td>
                            <td><?php echo e($item->sku); ?></td>
                            <td>
                                <span class="badge badge-info"><?php echo e($item->current_stock); ?> <?php echo e($item->unit); ?></span>
                            </td>
                            <td><?php echo e($item->min_stock_threshold); ?> <?php echo e($item->unit); ?></td>
                            <td><?php echo e($item->category); ?></td>
                            <td>
                                <span class="badge badge-info">
                                    <i class="fas fa-exclamation-triangle"></i> Stok Rendah
                                </span>
                            </td>
                            <td>
                                <a href="<?php echo e(route('other-products.show', $item->id)); ?>" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i> Detail
                                </a>
                                <a href="<?php echo e(route('other-products.edit', $item->id)); ?>" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if($lowStockRaw->count() == 0 && $lowStockProcessed->count() == 0 && $lowStockOther->count() == 0): ?>
    <div class="card shadow">
        <div class="card-body text-center py-5">
            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
            <h4 class="text-success">Semua Stok Aman!</h4>
            <p class="text-muted">Tidak ada produk dengan stok menipis saat ini.</p>
            <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-success">
                <i class="fas fa-arrow-left"></i> Kembali ke Dashboard
            </a>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\WEB\iterasi 2\ubi-bakar-cilembu\resources\views/inventory/low-stock-alert.blade.php ENDPATH**/ ?>