<?php

echo "🧪 TESTING PAYMENT CONTROLLER ISSUE\n";
echo "===================================\n\n";

// Test URLs
$baseUrl = 'http://127.0.0.1:8000';
$testUrls = [
    'Debug endpoint' => $baseUrl . '/payment/debug',
    'Test endpoint (GET)' => $baseUrl . '/payment/test/26',
    'POS page' => $baseUrl . '/pos'
];

echo "1. Testing basic endpoints...\n";

foreach ($testUrls as $name => $url) {
    echo "   🌐 Testing {$name}: {$url}\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_COOKIEJAR, 'test_cookies.txt');
    curl_setopt($ch, CURLOPT_COOKIEFILE, 'test_cookies.txt');
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    if ($error) {
        echo "      ❌ Error: {$error}\n";
    } else {
        echo "      ✅ HTTP {$httpCode} ({$duration}ms)\n";
        
        if ($httpCode == 200) {
            // Try to parse JSON
            $responseData = json_decode($response, true);
            if ($responseData) {
                echo "      📊 JSON Response:\n";
                echo "         Success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
                echo "         Message: " . ($responseData['message'] ?? 'no message') . "\n";
                
                if (isset($responseData['test_results'])) {
                    echo "         Test Results:\n";
                    foreach ($responseData['test_results'] as $test => $result) {
                        $status = $result ? '✅' : '❌';
                        echo "           {$status} {$test}: " . ($result ? 'pass' : 'fail') . "\n";
                    }
                }
            } else {
                echo "      📄 HTML Response: " . substr($response, 0, 100) . "...\n";
            }
        } elseif ($httpCode == 302) {
            echo "      🔄 Redirect (probably to login)\n";
        } elseif ($httpCode == 419) {
            echo "      🔒 CSRF token required\n";
        } elseif ($httpCode == 500) {
            echo "      💥 Server Error\n";
            echo "      📄 Response: " . substr($response, 0, 200) . "...\n";
        }
    }
    echo "\n";
}

echo "2. Testing POST request with CSRF token...\n";

// First get CSRF token from POS page
echo "   📝 Getting CSRF token from POS page...\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $baseUrl . '/pos');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_COOKIEJAR, 'test_cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, 'test_cookies.txt');

$posResponse = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    echo "   ❌ Failed to get POS page: HTTP {$httpCode}\n";
    exit(1);
}

// Extract CSRF token
preg_match('/name="csrf-token" content="([^"]+)"/', $posResponse, $matches);
$csrfToken = $matches[1] ?? '';

if (empty($csrfToken)) {
    echo "   ❌ CSRF token not found\n";
    echo "   📄 Response sample: " . substr($posResponse, 0, 500) . "...\n";
} else {
    echo "   ✅ CSRF token found: " . substr($csrfToken, 0, 20) . "...\n";
    
    // Test POST request to payment create
    echo "   🚀 Testing POST to payment/create/26...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . '/payment/create/26');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    curl_setopt($ch, CURLOPT_COOKIEFILE, 'test_cookies.txt');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'X-CSRF-TOKEN: ' . $csrfToken,
        'X-Requested-With: XMLHttpRequest'
    ]);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    if ($error) {
        echo "      ❌ cURL Error: {$error}\n";
    } else {
        echo "      ✅ HTTP {$httpCode} ({$duration}ms)\n";
        
        if ($duration > 30000) {
            echo "      ⚠️  WARNING: Very slow response ({$duration}ms)\n";
        } elseif ($duration > 10000) {
            echo "      ⚠️  WARNING: Slow response ({$duration}ms)\n";
        }
        
        echo "      📄 Response: " . substr($response, 0, 300) . "...\n";
        
        $responseData = json_decode($response, true);
        if ($responseData) {
            echo "      📊 JSON Response:\n";
            echo "         Success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
            echo "         Message: " . ($responseData['message'] ?? 'no message') . "\n";
            
            if (isset($responseData['error_detail'])) {
                echo "         Error Detail: {$responseData['error_detail']}\n";
            }
        }
    }
}

// Cleanup
@unlink('test_cookies.txt');

echo "\n🏁 Test completed!\n\n";

echo "💡 ANALYSIS:\n";
echo "============\n";
echo "1. If debug endpoint works: Controller is accessible\n";
echo "2. If test endpoint works: Route binding is working\n";
echo "3. If POST request hangs: Issue is in MidtransService or network\n";
echo "4. If CSRF errors: Frontend token handling issue\n";
echo "5. If authentication errors: User needs to login\n\n";

echo "🔧 NEXT STEPS:\n";
echo "==============\n";
echo "1. Check Laravel logs: storage/logs/laravel.log\n";
echo "2. Monitor browser console for JavaScript errors\n";
echo "3. Use browser Network tab to see actual request/response\n";
echo "4. Try with debug=1 parameter for quick response\n\n";
