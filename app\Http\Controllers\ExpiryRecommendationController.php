<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProcessedInventory;
use App\Models\Distribution;
use App\Exports\ExpiryRecommendationExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class ExpiryRecommendationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the recommendations.
     */
    public function index()
    {
        // Update expiry tracking data for all processed inventory items
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        // Summary stats
        $highPriorityCount = $recommendedItems->where('priority_level', 'Tinggi')->count();
        $mediumPriorityCount = $recommendedItems->where('priority_level', 'Sedang')->count();
        $lowPriorityCount = $recommendedItems->where('priority_level', 'Rendah')->count();
        $totalPotentialLoss = $recommendedItems->where('priority_level', 'Tinggi')
                                               ->sum(function($item) {
                                                   return $item->current_stock * $item->cost_per_unit;
                                               });
        
        // Get available markets for distribution
        $markets = Distribution::select('market_name')
                               ->distinct()
                               ->orderBy('market_name')
                               ->pluck('market_name');
        
        return view('inventory.expiry-recommendations', compact(
            'recommendedItems',
            'highPriorityCount',
            'mediumPriorityCount',
            'lowPriorityCount',
            'totalPotentialLoss',
            'markets'
        ));
    }
    
    /**
     * Update recommendation for a specific item
     */
    public function updateRecommendation($id)
    {
        $item = ProcessedInventory::findOrFail($id);
        $item->updateExpiryTracking();
        
        return redirect()->back()->with('success', 'Rekomendasi berhasil diperbarui');
    }
    
    /**
     * Update all recommendations
     */
    public function updateAllRecommendations()
    {
        $items = ProcessedInventory::where('is_active', true)
                                   ->where('current_stock', '>', 0)
                                   ->where('expiry_date', '!=', null)
                                   ->get();
                                   
        foreach($items as $item) {
            $item->updateExpiryTracking();
        }
        
        return redirect()->back()->with('success', 'Semua rekomendasi berhasil diperbarui');
    }
    
    /**
     * Export recommendations to Excel
     */
    public function exportExcel()
    {
        return Excel::download(new ExpiryRecommendationExport, 'rekomendasi-penjualan-ubi-matang-' . now()->format('Y-m-d') . '.xlsx');
    }
    
    /**
     * Export recommendations to PDF
     */
    public function exportPdf()
    {
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        $pdf = PDF::loadView('exports.expiry-recommendations-pdf', [
            'recommendedItems' => $recommendedItems,
            'date' => now()->format('d M Y')
        ]);
        
        return $pdf->download('rekomendasi-penjualan-ubi-matang-' . now()->format('Y-m-d') . '.pdf');
    }
    
    /**
     * Set recommended market for an item
     */
    public function setMarket(Request $request, $id)
    {
        $request->validate([
            'market_name' => 'required|string|max:255'
        ]);
        
        $item = ProcessedInventory::findOrFail($id);
        $item->recommended_market = $request->market_name;
        $item->save();
        
        return redirect()->back()->with('success', 'Market untuk distribusi berhasil diperbarui');
    }

    /**
     * Show form to distribute expiring product
     */
    public function showDistributeForm($id)
    {
        $item = ProcessedInventory::findOrFail($id);

        // Get available markets from previous distributions
        $markets = Distribution::select('destination')
                               ->distinct()
                               ->orderBy('destination')
                               ->pluck('destination');

        return view('inventory.expiry-distribute', compact('item', 'markets'));
    }

    /**
     * Process distribution for expiring product
     */
    public function processDistribute(Request $request, $id)
    {
        $request->validate([
            'destination' => 'required|string|max:255',
            'quantity' => 'required|integer|min:1',
            'distribution_date' => 'required|date|after_or_equal:today',
            'vehicle_info' => 'nullable|string|max:255',
            'driver_name' => 'nullable|string|max:255',
            'notes' => 'nullable|string',
            'urgent_distribution' => 'boolean'
        ]);

        $item = ProcessedInventory::findOrFail($id);

        // Validate quantity
        if ($request->quantity > $item->current_stock) {
            return back()->withErrors(['quantity' => 'Jumlah melebihi stok tersedia'])
                        ->withInput();
        }

        DB::beginTransaction();

        try {
            // Create distribution record
            $distribution = Distribution::create([
                'distribution_number' => 'URGENT-' . date('YmdHis'),
                'user_id' => Auth::id(),
                'destination' => $request->destination,
                'distribution_date' => $request->distribution_date,
                'vehicle_info' => $request->vehicle_info,
                'driver_name' => $request->driver_name,
                'notes' => $request->notes . ' [URGENT - Produk mendekati kadaluarsa]',
                'status' => 'planned',
                'is_urgent' => true
            ]);

            // Create distribution item
            \App\Models\DistributionItem::create([
                'distribution_id' => $distribution->id,
                'processed_inventory_id' => $item->id,
                'quantity' => $request->quantity,
                'price_per_item' => $item->selling_price,
                'total_price' => $request->quantity * $item->selling_price
            ]);

            // Update stock
            $item->current_stock -= $request->quantity;
            $item->save();

            // Update expiry tracking
            $item->updateExpiryTracking();

            DB::commit();

            return redirect()->route('expiry-recommendations.index')
                           ->with('success', 'Distribusi urgent berhasil dibuat untuk produk yang mendekati kadaluarsa');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                        ->withInput();
        }
    }
}
