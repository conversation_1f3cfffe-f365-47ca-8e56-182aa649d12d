<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ProcessedInventory;
use App\Models\Distribution;
use App\Exports\ExpiryRecommendationExport;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Barryvdh\DomPDF\Facade\Pdf;

class ExpiryRecommendationController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    
    /**
     * Display a listing of the recommendations.
     */
    public function index()
    {
        // Update expiry tracking data for all processed inventory items
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        // Summary stats
        $highPriorityCount = $recommendedItems->where('priority_level', 'Tinggi')->count();
        $mediumPriorityCount = $recommendedItems->where('priority_level', 'Sedang')->count();
        $lowPriorityCount = $recommendedItems->where('priority_level', 'Rendah')->count();
        $totalPotentialLoss = $recommendedItems->where('priority_level', 'Tinggi')
                                               ->sum(function($item) {
                                                   return $item->current_stock * $item->cost_per_unit;
                                               });
        
        // Get available markets for distribution
        $markets = Distribution::select('market_name')
                               ->distinct()
                               ->orderBy('market_name')
                               ->pluck('market_name');
        
        return view('inventory.expiry-recommendations', compact(
            'recommendedItems',
            'highPriorityCount',
            'mediumPriorityCount',
            'lowPriorityCount',
            'totalPotentialLoss',
            'markets'
        ));
    }
    
    /**
     * Update recommendation for a specific item
     */
    public function updateRecommendation($id)
    {
        $item = ProcessedInventory::findOrFail($id);
        $item->updateExpiryTracking();
        
        return redirect()->back()->with('success', 'Rekomendasi berhasil diperbarui');
    }
    
    /**
     * Update all recommendations
     */
    public function updateAllRecommendations()
    {
        $items = ProcessedInventory::where('is_active', true)
                                   ->where('current_stock', '>', 0)
                                   ->where('expiry_date', '!=', null)
                                   ->get();
                                   
        foreach($items as $item) {
            $item->updateExpiryTracking();
        }
        
        return redirect()->back()->with('success', 'Semua rekomendasi berhasil diperbarui');
    }
    
    /**
     * Export recommendations to Excel
     */
    public function exportExcel()
    {
        return Excel::download(new ExpiryRecommendationExport, 'rekomendasi-penjualan-ubi-matang-' . now()->format('Y-m-d') . '.xlsx');
    }
    
    /**
     * Export recommendations to PDF
     */
    public function exportPdf()
    {
        $recommendedItems = ProcessedInventory::getExpiryTrackingList();
        
        $pdf = PDF::loadView('exports.expiry-recommendations-pdf', [
            'recommendedItems' => $recommendedItems,
            'date' => now()->format('d M Y')
        ]);
        
        return $pdf->download('rekomendasi-penjualan-ubi-matang-' . now()->format('Y-m-d') . '.pdf');
    }
    
    /**
     * Set recommended market for an item
     */
    public function setMarket(Request $request, $id)
    {
        $request->validate([
            'market_name' => 'required|string|max:255'
        ]);
        
        $item = ProcessedInventory::findOrFail($id);
        $item->recommended_market = $request->market_name;
        $item->save();
        
        return redirect()->back()->with('success', 'Market untuk distribusi berhasil diperbarui');
    }



    /**
     * Show sales report
     */
    public function salesReport(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', now()->format('Y-m-d'));
        $marketFilter = $request->get('market');

        // Base query for distributions
        $query = Distribution::with(['items.processedInventory'])
                            ->whereBetween('distribution_date', [$startDate, $endDate]);

        if ($marketFilter) {
            $query->where('destination', $marketFilter);
        }

        $distributions = $query->orderBy('distribution_date', 'desc')->paginate(20);

        // Calculate statistics
        $totalQuantitySold = \App\Models\DistributionItem::whereHas('distribution', function($q) use ($startDate, $endDate, $marketFilter) {
            $q->whereBetween('distribution_date', [$startDate, $endDate]);
            if ($marketFilter) {
                $q->where('destination', $marketFilter);
            }
        })->sum('quantity');

        $totalRevenue = \App\Models\DistributionItem::whereHas('distribution', function($q) use ($startDate, $endDate, $marketFilter) {
            $q->whereBetween('distribution_date', [$startDate, $endDate]);
            if ($marketFilter) {
                $q->where('destination', $marketFilter);
            }
        })->sum('total_price');

        $totalMarkets = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
                                  ->when($marketFilter, function($q) use ($marketFilter) {
                                      return $q->where('destination', $marketFilter);
                                  })
                                  ->distinct('destination')
                                  ->count('destination');

        $totalDistributions = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
                                        ->when($marketFilter, function($q) use ($marketFilter) {
                                            return $q->where('destination', $marketFilter);
                                        })
                                        ->count();

        $averagePerDistribution = $totalDistributions > 0 ? round($totalQuantitySold / $totalDistributions) : 0;

        // Market data for chart
        $marketData = Distribution::select('destination as market')
                                ->selectRaw('SUM(distribution_items.quantity) as total_quantity')
                                ->join('distribution_items', 'distributions.id', '=', 'distribution_items.distribution_id')
                                ->whereBetween('distribution_date', [$startDate, $endDate])
                                ->when($marketFilter, function($q) use ($marketFilter) {
                                    return $q->where('destination', $marketFilter);
                                })
                                ->groupBy('destination')
                                ->orderBy('total_quantity', 'desc')
                                ->get();

        // Daily data for chart
        $dailyData = Distribution::select(\DB::raw('DATE(distribution_date) as date'))
                                ->selectRaw('SUM(distribution_items.quantity) as total_quantity')
                                ->join('distribution_items', 'distributions.id', '=', 'distribution_items.distribution_id')
                                ->whereBetween('distribution_date', [$startDate, $endDate])
                                ->when($marketFilter, function($q) use ($marketFilter) {
                                    return $q->where('destination', $marketFilter);
                                })
                                ->groupBy(\DB::raw('DATE(distribution_date)'))
                                ->orderBy('date')
                                ->get()
                                ->map(function($item) {
                                    $item->date = \Carbon\Carbon::parse($item->date)->format('d/m');
                                    return $item;
                                });

        // Get all markets for filter
        $markets = Distribution::select('destination')
                              ->distinct()
                              ->orderBy('destination')
                              ->pluck('destination');

        return view('inventory.sales-report', compact(
            'distributions',
            'totalQuantitySold',
            'totalRevenue',
            'totalMarkets',
            'averagePerDistribution',
            'marketData',
            'dailyData',
            'markets'
        ));
    }

    /**
     * Create distribution for expiring product
     */
    public function createDistribution(Request $request, $id)
    {
        $request->validate([
            'destination' => 'required|string|max:255',
            'quantity' => 'required|integer|min:1',
            'distribution_date' => 'required|date|after_or_equal:today',
            'notes' => 'nullable|string'
        ]);

        $item = ProcessedInventory::findOrFail($id);

        // Validate quantity
        if ($request->quantity > $item->current_stock) {
            return back()->withErrors(['quantity' => 'Jumlah melebihi stok tersedia'])
                        ->withInput();
        }

        DB::beginTransaction();

        try {
            // Determine if this is urgent based on expiry date
            $daysUntilExpiry = \Carbon\Carbon::parse($item->expiry_date)->diffInDays(now(), false);
            $isUrgent = $daysUntilExpiry <= 3;

            // Create distribution record
            $distributionNumber = $isUrgent ? 'URGENT-' . date('YmdHis') : 'DIST-' . date('YmdHis');
            $notes = $request->notes;
            if ($isUrgent) {
                $notes .= ' [PRIORITAS TINGGI - Produk mendekati kadaluarsa]';
            }

            $distribution = Distribution::create([
                'distribution_number' => $distributionNumber,
                'user_id' => Auth::id(),
                'destination' => $request->destination,
                'market_name' => $request->destination, // For compatibility
                'distribution_date' => $request->distribution_date,
                'notes' => $notes,
                'status' => 'planned',
                'is_urgent' => $isUrgent
            ]);

            // Create distribution item
            \App\Models\DistributionItem::create([
                'distribution_id' => $distribution->id,
                'processed_inventory_id' => $item->id,
                'quantity' => $request->quantity,
                'price_per_item' => $item->selling_price,
                'total_price' => $request->quantity * $item->selling_price
            ]);

            // Update stock
            $item->current_stock -= $request->quantity;
            $item->save();

            // Update expiry tracking
            $item->updateExpiryTracking();

            DB::commit();

            $message = $isUrgent
                ? 'Distribusi prioritas tinggi berhasil dibuat untuk produk yang mendekati kadaluarsa'
                : 'Distribusi berhasil dibuat';

            return redirect()->route('expiry-recommendations.index')
                           ->with('success', $message);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                        ->withInput();
        }
    }
}
