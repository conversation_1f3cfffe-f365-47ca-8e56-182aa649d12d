<?php

require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Services\MidtransService;
use Illuminate\Support\Facades\Log;

echo "🔍 DEBUGGING PAYMENT GATEWAY ISSUE\n";
echo "===================================\n\n";

// Step 1: Check if we have a valid transaction
echo "1. Checking for existing transactions...\n";
$transaction = Transaction::where('status', 'pending')
    ->where('payment_method', 'gateway')
    ->with('items')
    ->first();

if (!$transaction) {
    echo "   No pending gateway transaction found. Creating test transaction...\n";
    
    // Create test transaction
    $transaction = Transaction::create([
        'user_id' => 1,
        'customer_name' => 'Test Customer',
        'customer_phone' => '081234567890',
        'subtotal' => 50000,
        'tax' => 0,
        'discount' => 0,
        'total_amount' => 50000,
        'amount_paid' => 0,
        'change_amount' => 0,
        'payment_method' => 'gateway',
        'status' => 'pending',
        'notes' => 'Test transaction for debugging'
    ]);
    
    // Add test item
    $product = ProcessedInventory::first();
    if ($product) {
        TransactionItem::create([
            'transaction_id' => $transaction->id,
            'processed_inventory_id' => $product->id,
            'product_name' => $product->name,
            'quantity' => 2,
            'price' => 25000,
            'subtotal' => 50000
        ]);
    }
    
    $transaction->load('items');
}

echo "   ✅ Using transaction: {$transaction->invoice_number}\n";
echo "   📄 Transaction ID: {$transaction->id}\n";
echo "   💰 Total Amount: Rp " . number_format($transaction->total_amount, 0, ',', '.') . "\n";
echo "   📦 Items Count: {$transaction->items->count()}\n\n";

// Step 2: Test direct API call
echo "2. Testing direct API call to payment gateway...\n";

try {
    $url = "http://localhost/ubi-bakar-cilembu/public/payment/create/{$transaction->id}";
    echo "   🌐 URL: {$url}\n";
    
    // Get CSRF token
    $tokenUrl = "http://localhost/ubi-bakar-cilembu/public/pos";
    $tokenResponse = file_get_contents($tokenUrl);
    preg_match('/name="csrf-token" content="([^"]+)"/', $tokenResponse, $matches);
    $csrfToken = $matches[1] ?? '';
    
    echo "   🔑 CSRF Token: " . substr($csrfToken, 0, 20) . "...\n";
    
    $postData = json_encode([]);
    
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Accept: application/json',
                'X-CSRF-TOKEN: ' . $csrfToken,
                'X-Requested-With: XMLHttpRequest'
            ],
            'content' => $postData,
            'timeout' => 60
        ]
    ]);
    
    echo "   ⏱️  Making request...\n";
    $startTime = microtime(true);
    
    $response = file_get_contents($url, false, $context);
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "   ✅ Response received in {$duration}ms\n";
    echo "   📄 Response: " . substr($response, 0, 200) . "...\n\n";
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "   📊 Parsed Response:\n";
        echo "      Success: " . ($responseData['success'] ? 'true' : 'false') . "\n";
        if (isset($responseData['message'])) {
            echo "      Message: {$responseData['message']}\n";
        }
        if (isset($responseData['redirect_url'])) {
            echo "      Redirect URL: {$responseData['redirect_url']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "   ❌ API call failed: " . $e->getMessage() . "\n\n";
}

// Step 3: Test MidtransService directly
echo "3. Testing MidtransService directly...\n";

try {
    $midtransService = new MidtransService();
    
    echo "   🔧 Creating snap token...\n";
    $startTime = microtime(true);
    
    $result = $midtransService->createSnapToken($transaction);
    
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    echo "   ⏱️  Duration: {$duration}ms\n";
    
    if ($result['success']) {
        echo "   ✅ Snap token created successfully!\n";
        echo "   🎫 Token: " . substr($result['snap_token'], 0, 30) . "...\n";
        echo "   🔗 Redirect URL: {$result['redirect_url']}\n\n";
    } else {
        echo "   ❌ Failed to create snap token\n";
        echo "   💬 Message: {$result['message']}\n";
        if (isset($result['error_detail'])) {
            echo "   🔍 Detail: {$result['error_detail']}\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ MidtransService error: " . $e->getMessage() . "\n\n";
}

// Step 4: Check logs
echo "4. Checking recent logs...\n";
$logFile = storage_path('logs/laravel.log');
if (file_exists($logFile)) {
    $logs = file_get_contents($logFile);
    $recentLogs = array_slice(explode("\n", $logs), -20);
    
    echo "   📋 Recent log entries:\n";
    foreach ($recentLogs as $log) {
        if (strpos($log, 'payment') !== false || strpos($log, 'Midtrans') !== false) {
            echo "      " . substr($log, 0, 100) . "...\n";
        }
    }
} else {
    echo "   ⚠️  Log file not found\n";
}

echo "\n";

// Step 5: Network connectivity test
echo "5. Testing network connectivity...\n";

$testUrls = [
    'Local POS' => 'http://localhost/ubi-bakar-cilembu/public/pos',
    'Midtrans Sandbox' => 'https://api.sandbox.midtrans.com/v2/ping'
];

foreach ($testUrls as $name => $url) {
    echo "   🌐 Testing {$name}: {$url}\n";
    
    $startTime = microtime(true);
    $response = @file_get_contents($url, false, stream_context_create([
        'http' => ['timeout' => 10]
    ]));
    $endTime = microtime(true);
    
    if ($response !== false) {
        $duration = round(($endTime - $startTime) * 1000, 2);
        echo "      ✅ Accessible ({$duration}ms)\n";
    } else {
        echo "      ❌ Not accessible\n";
    }
}

echo "\n🏁 Debug completed!\n";

// Step 6: Recommendations
echo "\n💡 RECOMMENDATIONS:\n";
echo "==================\n";

if (isset($result) && !$result['success']) {
    echo "1. ❌ Midtrans token creation failed\n";
    echo "   - Check Midtrans credentials in .env\n";
    echo "   - Verify network connectivity\n";
    echo "   - Check server logs for detailed errors\n\n";
}

echo "2. 🔧 To fix loading issue:\n";
echo "   - Check browser console for JavaScript errors\n";
echo "   - Verify CSRF token is being sent correctly\n";
echo "   - Check if middleware is blocking the request\n";
echo "   - Monitor network tab in browser dev tools\n\n";

echo "3. 🧪 Testing steps:\n";
echo "   - Open browser dev tools (F12)\n";
echo "   - Go to Network tab\n";
echo "   - Try payment gateway again\n";
echo "   - Check if request is being sent\n";
echo "   - Look for any error responses\n\n";
