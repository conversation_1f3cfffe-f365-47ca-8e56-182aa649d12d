# 💳 PANDUAN TESTING PAYMENT GATEWAY MIDTRANS

## 🚀 QUICK START

### 1. Start Development Server
```bash
# Windows
start_server.bat

# Linux/Mac
php artisan serve
```

### 2. Aks<PERSON> POS System
- Buka: http://localhost:8000/pos
- Login dengan akun admin
- <PERSON><PERSON> testing payment gateway

## 🧪 TESTING SCENARIOS

### ✅ SCENARIO 1: SUCCESS PAYMENT
1. **Buka POS** → http://localhost:8000/pos
2. **Tambahkan produk** ke keranjang
3. **Klik "Proses Pembayaran"**
4. **Pilih tab "Payment Gateway"**
5. **Klik "Lanjutkan ke Payment Gateway"**
6. **Pilih "Credit Card"** di halaman Midtrans
7. **Gunakan test card:**
   - Card Number: `4811 1111 1111 1114`
   - CVV: `123`
   - Exp Date: `01/25`
8. **<PERSON><PERSON> "Pay Now"**
9. **Verifikasi:** Status transaksi berubah menjadi "Completed"

### ❌ SCENARIO 2: FAILED PAYMENT
1. **Ulangi langkah 1-6** dari scenario 1
2. **<PERSON><PERSON><PERSON> failed test card:**
   - Card Number: `4911 1111 1111 1113`
   - CVV: `123`
   - Exp Date: `01/25`
3. **Verifikasi:** Payment akan gagal
4. **Cek:** Status transaksi tetap "Pending" atau berubah "Failed"

### ⏳ SCENARIO 3: PENDING PAYMENT
1. **Ulangi langkah 1-6** dari scenario 1
2. **Gunakan pending test card:**
   - Card Number: `4411 1111 1111 1118`
   - CVV: `123`
   - Exp Date: `01/25`
3. **Verifikasi:** Payment akan pending
4. **Cek:** Status transaksi "Pending"

### 🏪 SCENARIO 4: E-WALLET PAYMENT
1. **Ulangi langkah 1-6** dari scenario 1
2. **Pilih "GoPay"** di halaman Midtrans
3. **Gunakan test phone:** `************`
4. **Simulasi pembayaran** di halaman test
5. **Verifikasi:** Status transaksi berubah sesuai hasil

### 🏦 SCENARIO 5: VIRTUAL ACCOUNT
1. **Ulangi langkah 1-6** dari scenario 1
2. **Pilih "BCA Virtual Account"**
3. **Catat nomor VA** yang digenerate
4. **Simulasi pembayaran** di halaman test
5. **Verifikasi:** Status transaksi berubah sesuai hasil

## 🔍 MONITORING & DEBUGGING

### 1. Cek Status Transaksi
- **Dashboard:** http://localhost:8000/transactions
- **Detail Transaksi:** Klik pada transaksi untuk melihat detail
- **Manual Check:** Tombol "Cek Status" untuk refresh status

### 2. Log Monitoring
```bash
# Monitor Laravel logs
tail -f storage/logs/laravel.log

# Monitor payment gateway logs
grep "Midtrans" storage/logs/laravel.log
```

### 3. Database Check
```sql
-- Cek transaksi pending
SELECT * FROM transactions WHERE status = 'pending';

-- Cek payment gateway transactions
SELECT * FROM transactions WHERE payment_gateway IS NOT NULL;

-- Cek transaction items
SELECT t.invoice_number, ti.product_name, ti.quantity, ti.price 
FROM transactions t 
JOIN transaction_items ti ON t.id = ti.transaction_id 
WHERE t.payment_method = 'gateway';
```

## 🛠️ TROUBLESHOOTING

### ❌ Problem: Payment URL tidak terbuka
**Solution:**
1. Cek kredensial Midtrans di `.env`
2. Pastikan internet connection aktif
3. Cek log error di `storage/logs/laravel.log`

### ❌ Problem: Status tidak update otomatis
**Solution:**
1. Setup webhook URL (untuk production)
2. Gunakan tombol "Cek Status" manual
3. Cek log webhook di dashboard Midtrans

### ❌ Problem: Stock tidak berkurang
**Solution:**
1. Pastikan payment status "completed"
2. Cek webhook notification
3. Manual update status jika perlu

## 📊 EXPECTED RESULTS

### ✅ Success Payment Flow:
```
1. Transaction created → Status: "pending"
2. Redirect to Midtrans → Payment page opens
3. Complete payment → Webhook received
4. Status updated → "completed"
5. Stock reduced → Inventory updated
6. Receipt available → Can print receipt
```

### ❌ Failed Payment Flow:
```
1. Transaction created → Status: "pending"
2. Redirect to Midtrans → Payment page opens
3. Payment fails → Webhook received
4. Status updated → "failed"
5. Stock restored → No inventory change
```

## 🎯 PRODUCTION CHECKLIST

### Before Going Live:
- [ ] Test all payment methods
- [ ] Verify webhook functionality
- [ ] Setup SSL certificate
- [ ] Update to production credentials
- [ ] Test with real small amounts
- [ ] Train staff on new system
- [ ] Setup monitoring & alerts
- [ ] Backup database before launch

## 📞 SUPPORT

### Midtrans Support:
- **Documentation:** https://docs.midtrans.com/
- **Dashboard:** https://dashboard.sandbox.midtrans.com/
- **Support:** <EMAIL>

### System Issues:
- Check Laravel logs
- Verify database connections
- Test API credentials
- Monitor server resources

---

**🎉 Happy Testing! Payment Gateway siap digunakan untuk production setelah semua test scenario berhasil.**
