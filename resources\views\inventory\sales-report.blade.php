@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON>')

@push('styles')
<style>
    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 20px;
        color: white;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-card.sales {
        background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    }
    
    .stat-card.revenue {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .stat-card.markets {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    .stat-card.avg {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    
    .table-container {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    
    .filter-card {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
</style>
@endpush

@section('content')
<div class="container-fluid px-4">
    <h1 class="mt-4">Laporan Penjualan Ubi ke Pasar</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li>
        <li class="breadcrumb-item"><a href="{{ route('expiry-recommendations.index') }}">Rekomendasi Ubi</a></li>
        <li class="breadcrumb-item active">Laporan Penjualan</li>
    </ol>
    
    <!-- Filter Section -->
    <div class="filter-card">
        <form method="GET" action="{{ route('expiry-recommendations.sales-report') }}">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="{{ request('start_date', now()->startOfMonth()->format('Y-m-d')) }}">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Tanggal Akhir</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="{{ request('end_date', now()->format('Y-m-d')) }}">
                </div>
                <div class="col-md-3">
                    <label for="market" class="form-label">Pasar</label>
                    <select class="form-select" id="market" name="market">
                        <option value="">Semua Pasar</option>
                        @foreach($markets as $market)
                            <option value="{{ $market }}" {{ request('market') == $market ? 'selected' : '' }}>
                                {{ $market }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <a href="{{ route('expiry-recommendations.sales-report') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-undo me-1"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card sales">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0 opacity-75">Total Ubi Terjual</h6>
                        <h2 class="my-2">{{ number_format($totalQuantitySold) }}</h2>
                        <p class="mb-0"><i class="fas fa-weight me-1"></i> Unit</p>
                    </div>
                    <div>
                        <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card revenue">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0 opacity-75">Total Pendapatan</h6>
                        <h2 class="my-2">Rp {{ number_format($totalRevenue, 0, ',', '.') }}</h2>
                        <p class="mb-0"><i class="fas fa-money-bill-wave me-1"></i> Rupiah</p>
                    </div>
                    <div>
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card markets">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0 opacity-75">Jumlah Pasar</h6>
                        <h2 class="my-2">{{ $totalMarkets }}</h2>
                        <p class="mb-0"><i class="fas fa-store me-1"></i> Pasar</p>
                    </div>
                    <div>
                        <i class="fas fa-map-marker-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="stat-card avg">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-0 opacity-75">Rata-rata per Distribusi</h6>
                        <h2 class="my-2">{{ number_format($averagePerDistribution) }}</h2>
                        <p class="mb-0"><i class="fas fa-calculator me-1"></i> Unit</p>
                    </div>
                    <div>
                        <i class="fas fa-chart-bar fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Charts Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-chart-pie me-2"></i> Distribusi per Pasar</h5>
                <canvas id="marketChart" width="400" height="300"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h5 class="mb-3"><i class="fas fa-chart-line me-2"></i> Trend Penjualan Harian</h5>
                <canvas id="dailyChart" width="400" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Detailed Table -->
    <div class="table-container">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-table me-2"></i> Detail Distribusi Ubi</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Tanggal</th>
                            <th>No. Distribusi</th>
                            <th>Produk</th>
                            <th>Pasar Tujuan</th>
                            <th>Jumlah</th>
                            <th>Harga Satuan</th>
                            <th>Total Nilai</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($distributions as $distribution)
                            @foreach($distribution->items as $item)
                                <tr>
                                    <td>{{ $distribution->distribution_date->format('d/m/Y') }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ $distribution->distribution_number }}</span>
                                        @if($distribution->is_urgent)
                                            <span class="badge bg-warning ms-1">URGENT</span>
                                        @endif
                                    </td>
                                    <td>
                                        <strong>{{ $item->processedInventory->name }}</strong><br>
                                        <small class="text-muted">{{ $item->processedInventory->batch_number }}</small>
                                    </td>
                                    <td>
                                        <i class="fas fa-map-marker-alt text-primary me-1"></i>
                                        {{ $distribution->destination }}
                                    </td>
                                    <td>{{ number_format($item->quantity) }} unit</td>
                                    <td>Rp {{ number_format($item->price_per_item, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($item->total_price, 0, ',', '.') }}</td>
                                    <td>
                                        @if($distribution->status === 'completed')
                                            <span class="badge bg-success">Selesai</span>
                                        @elseif($distribution->status === 'in_progress')
                                            <span class="badge bg-warning">Dalam Perjalanan</span>
                                        @else
                                            <span class="badge bg-secondary">Direncanakan</span>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i><br>
                                    Tidak ada data distribusi dalam periode yang dipilih.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    @if($distributions->hasPages())
        <div class="d-flex justify-content-center mt-4">
            {{ $distributions->appends(request()->query())->links() }}
        </div>
    @endif
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Market Distribution Chart
    const marketCtx = document.getElementById('marketChart').getContext('2d');
    const marketChart = new Chart(marketCtx, {
        type: 'doughnut',
        data: {
            labels: {!! json_encode($marketData->pluck('market')) !!},
            datasets: [{
                data: {!! json_encode($marketData->pluck('total_quantity')) !!},
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', 
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
    
    // Daily Sales Chart
    const dailyCtx = document.getElementById('dailyChart').getContext('2d');
    const dailyChart = new Chart(dailyCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode($dailyData->pluck('date')) !!},
            datasets: [{
                label: 'Jumlah Terjual (Unit)',
                data: {!! json_encode($dailyData->pluck('total_quantity')) !!},
                borderColor: '#36A2EB',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
</script>
@endpush
