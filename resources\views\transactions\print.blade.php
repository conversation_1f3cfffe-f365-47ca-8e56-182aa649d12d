<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Receipt #{{ $transaction->invoice_number }}</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 0;
            padding: 10px;
            width: 80mm;
        }
        .receipt {
            width: 100%;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .header {
            margin-bottom: 10px;
            text-align: center;
        }
        .header h2 {
            margin: 0;
            padding: 0;
            font-size: 16px;
        }
        .header p {
            margin: 0;
            padding: 0;
        }
        .divider {
            border-top: 1px dashed #000;
            margin: 10px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        table th, table td {
            padding: 3px 0;
        }
        .total {
            font-weight: bold;
        }
        .footer {
            margin-top: 10px;
            text-align: center;
        }
        
        @media print {
            body {
                width: 80mm;
                margin: 0;
            }
            .no-print {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="receipt">
        <div class="header">
            <h2>UBI BAKAR CILEMBU</h2>
            <p>Jl. Contoh No. 123, Kota, Indonesia</p>
            <p>Telp: 081234567890</p>
        </div>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <td>No. Invoice</td>
                <td>:</td>
                <td>{{ $transaction->invoice_number }}</td>
            </tr>
            <tr>
                <td>Tanggal</td>
                <td>:</td>
                <td>{{ $transaction->created_at->format('d/m/Y H:i') }}</td>
            </tr>
            <tr>
                <td>Kasir</td>
                <td>:</td>
                <td>{{ $transaction->user->name ?? 'Admin' }}</td>
            </tr>
        </table>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <th class="text-left">Item</th>
                <th class="text-right">Qty</th>
                <th class="text-right">Harga</th>
                <th class="text-right">Subtotal</th>
            </tr>
            
            @foreach($transaction->items as $item)
            <tr>
                <td>{{ $item->product_name }}</td>
                <td class="text-right">{{ $item->quantity }}</td>
                <td class="text-right">{{ number_format($item->price, 0, ',', '.') }}</td>
                <td class="text-right">{{ number_format($item->subtotal, 0, ',', '.') }}</td>
            </tr>
            @endforeach
        </table>
        
        <div class="divider"></div>
        
        <table>
            <tr>
                <td>Total</td>
                <td class="text-right total">Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
            </tr>
            @if($transaction->payment_method === 'cash' && $transaction->amount_paid)
            <tr>
                <td>Bayar ({{ ucfirst($transaction->payment_method) }})</td>
                <td class="text-right">Rp {{ number_format($transaction->amount_paid, 0, ',', '.') }}</td>
            </tr>
            <tr>
                <td>Kembali</td>
                <td class="text-right">Rp {{ number_format($transaction->amount_paid - $transaction->total_amount, 0, ',', '.') }}</td>
            </tr>
            @else
            <tr>
                <td>Metode Bayar</td>
                <td class="text-right">{{ ucfirst($transaction->payment_method) }}</td>
            </tr>
            @endif
        </table>
        
        <div class="divider"></div>
        
        <div class="footer">
            <p>Terima Kasih Atas Kunjungan Anda</p>
            <p>Selamat Menikmati!</p>
        </div>
        
        <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()">Print Receipt</button>
            <button onclick="window.close()">Close</button>
        </div>
    </div>
    
    <script>
        window.onload = function() {
            // Automatically print when page loads
            setTimeout(function() {
                window.print();
            }, 500);
        };
    </script>
</body>
</html> 