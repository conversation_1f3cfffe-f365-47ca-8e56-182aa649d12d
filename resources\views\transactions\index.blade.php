@extends('layouts.app')

@php
use Illuminate\Support\Facades\Auth;
@endphp

@section('content')
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-cash-register"></i>
        <span>Daftar Transaksi</span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Riwayat Transaksi</span>
                    <div>
                        <a href="{{ route('pos') }}" class="btn btn-primary">
                            <i class="fas fa-shopping-cart"></i> Buat Transaksi Baru
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <form action="{{ route('transactions.index') }}" method="GET" class="row">
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">Dari</span>
                                    <input type="date" class="form-control" name="start_date" value="{{ request('start_date') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text">Sampai</span>
                                    <input type="date" class="form-control" name="end_date" value="{{ request('end_date') }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" placeholder="Cari transaksi..." value="{{ request('search') }}">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <a href="{{ route('transactions.index') }}" class="btn btn-secondary w-100">Reset</a>
                            </div>
                        </form>
                    </div>

                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>No. Invoice</th>
                                    <th>Tanggal</th>
                                    <th>Jumlah Item</th>
                                    <th>Total</th>
                                    <th>Kasir</th>
                                    <th>Metode Pembayaran</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($transactions as $transaction)
                                <tr>
                                    <td>{{ $transaction->invoice_number }}</td>
                                    <td>{{ $transaction->created_at->format('d M Y H:i') }}</td>
                                    <td>{{ $transaction->items_count }} item</td>
                                    <td>Rp {{ number_format($transaction->total_amount, 0, ',', '.') }}</td>
                                    <td>{{ $transaction->user->name ?? 'Admin' }}</td>
                                    <td>{{ $transaction->payment_method }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('transactions.show', $transaction) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('transactions.print', $transaction) }}" class="btn btn-sm btn-secondary" target="_blank">
                                                <i class="fas fa-print"></i>
                                            </a>
                                            @if(Auth::user()->isAdmin())
                                            <form action="{{ route('transactions.destroy', $transaction) }}" method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus transaksi ini?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="7" class="text-center">Tidak ada data transaksi</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $transactions->appends(request()->query())->links() }}
                    </div>

                    <div class="card mt-4">
                        <div class="card-body">
                            <h5 class="card-title">Ringkasan</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="border rounded p-3 mb-3">
                                        <h6>Total Transaksi</h6>
                                        <h3>{{ $transactionSummary['count'] }}</h3>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="border rounded p-3 mb-3">
                                        <h6>Total Pendapatan</h6>
                                        <h3>Rp {{ number_format($transactionSummary['total'], 0, ',', '.') }}</h3>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="border rounded p-3 mb-3">
                                        <h6>Rata-rata per Transaksi</h6>
                                        <h3>Rp {{ number_format($transactionSummary['average'], 0, ',', '.') }}</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection