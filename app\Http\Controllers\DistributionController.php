<?php

namespace App\Http\Controllers;

use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\ProcessedInventory;
use App\Models\OtherProduct;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class DistributionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $distributions = Distribution::with(['user', 'items'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('distributions.index', compact('distributions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $processedItems = ProcessedInventory::where('current_stock', '>', 0)
            ->where('is_active', true)
            ->get();
            
        $otherProducts = OtherProduct::where('current_stock', '>', 0)
            ->where('is_active', true)
            ->get();
            
        return view('distribution.create', compact('processedItems', 'otherProducts'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'market_name' => 'required|string|max:255',
            'distribution_date' => 'required|date',
            'notes' => 'nullable|string',
            'status' => 'required|in:planned,in_transit,delivered,returned',
            'items' => 'required|array|min:1',
            'items.*.type' => 'required|in:processed,other',
            'items.*.id' => 'required|integer',
            'items.*.quantity' => 'required|integer|min:1',
            'items.*.price' => 'required|numeric|min:0'
        ]);
        
        DB::beginTransaction();
        
        try {
            // Create distribution record
            $distribution = Distribution::create([
                'distribution_number' => Distribution::generateDistributionNumber(),
                'user_id' => Auth::id(),
                'market_name' => $validated['market_name'],
                'distribution_date' => $validated['distribution_date'],
                'notes' => $validated['notes'],
                'status' => $validated['status']
            ]);
            
            // Process items
            foreach ($validated['items'] as $item) {
                if ($item['type'] === 'processed') {
                    $product = ProcessedInventory::findOrFail($item['id']);
                    
                    // Check if enough stock
                    if ($product->current_stock < $item['quantity']) {
                        throw new \Exception("Stok tidak mencukupi untuk produk: {$product->name}");
                    }
                    
                    // Create distribution item
                    DistributionItem::create([
                        'distribution_id' => $distribution->id,
                        'processed_inventory_id' => $product->id,
                        'other_product_id' => null,
                        'quantity' => $item['quantity'],
                        'price_per_item' => $item['price'],
                        'total_price' => $item['price'] * $item['quantity']
                    ]);
                    
                    // Update stock
                    $product->current_stock -= $item['quantity'];
                    $product->save();
                } else {
                    $product = OtherProduct::findOrFail($item['id']);
                    
                    // Check if enough stock
                    if ($product->current_stock < $item['quantity']) {
                        throw new \Exception("Stok tidak mencukupi untuk produk: {$product->name}");
                    }
                    
                    // Create distribution item
                    DistributionItem::create([
                        'distribution_id' => $distribution->id,
                        'processed_inventory_id' => null,
                        'other_product_id' => $product->id,
                        'quantity' => $item['quantity'],
                        'price_per_item' => $item['price'],
                        'total_price' => $item['price'] * $item['quantity']
                    ]);
                    
                    // Update stock
                    $product->current_stock -= $item['quantity'];
                    $product->save();
                }
            }
            
            DB::commit();
            
            return redirect()->route('distribution.index')
                ->with('success', 'Distribusi berhasil dicatat');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Distribution $distribution)
    {
        $distribution->load(['user', 'items.processedInventory', 'items.otherProduct']);

        return view('distributions.show', compact('distribution'));
    }

    /**
     * Update the status of a distribution.
     */
    public function updateStatus(Request $request, Distribution $distribution)
    {
        $validated = $request->validate([
            'status' => 'required|in:planned,in_transit,delivered,returned'
        ]);
        
        $distribution->status = $validated['status'];
        
        // If returned, return items to inventory
        if ($validated['status'] === 'returned') {
            DB::beginTransaction();
            
            try {
                foreach ($distribution->items as $item) {
                    if ($item->processed_inventory_id) {
                        $product = $item->processedInventory;
                        $product->current_stock += $item->quantity;
                        $product->save();
                    } elseif ($item->other_product_id) {
                        $product = $item->otherProduct;
                        $product->current_stock += $item->quantity;
                        $product->save();
                    }
                }
                
                $distribution->save();
                DB::commit();
                
                return redirect()->route('distribution.show', $distribution)
                    ->with('success', 'Status distribusi berhasil diperbarui dan stok telah dikembalikan');
            } catch (\Exception $e) {
                DB::rollBack();
                return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()]);
            }
        } else {
            $distribution->save();
            
            return redirect()->route('distribution.show', $distribution)
                ->with('success', 'Status distribusi berhasil diperbarui');
        }
    }

    /**
     * Generate distribution report
     */
    public function report(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        
        $distributions = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->with(['user', 'items.processedInventory', 'items.otherProduct'])
            ->orderBy('distribution_date', 'desc')
            ->get();
            
        $totalDistributed = $distributions->count();
        $totalItems = DistributionItem::whereHas('distribution', function($query) use ($startDate, $endDate) {
                $query->whereBetween('distribution_date', [$startDate, $endDate]);
            })->sum('quantity');
            
        $totalValue = DistributionItem::whereHas('distribution', function($query) use ($startDate, $endDate) {
                $query->whereBetween('distribution_date', [$startDate, $endDate]);
            })->sum('total_price');
        
        // Get daily distribution data for chart
        $dailyData = Distribution::whereBetween('distribution_date', [$startDate, $endDate])
            ->select(DB::raw('DATE(distribution_date) as date'), DB::raw('COUNT(*) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $labels = $dailyData->pluck('date')->toArray();
        $data = $dailyData->pluck('total')->toArray();
        
        return view('distribution.report', compact(
            'distributions', 
            'startDate', 
            'endDate', 
            'totalDistributed',
            'totalItems',
            'totalValue',
            'labels',
            'data'
        ));
    }
}
