<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 TESTING MIDTRANS CONFIGURATION\n";
echo "=================================\n\n";

// Test 1: Check .env values
echo "1. Checking .env configuration...\n";

$envVars = [
    'MIDTRANS_MERCHANT_ID' => env('MIDTRANS_MERCHANT_ID'),
    'MIDTRANS_CLIENT_KEY' => env('MIDTRANS_CLIENT_KEY'),
    'MIDTRANS_SERVER_KEY' => env('MIDTRANS_SERVER_KEY'),
    'MIDTRANS_IS_PRODUCTION' => env('MIDTRANS_IS_PRODUCTION'),
];

foreach ($envVars as $key => $value) {
    if ($value) {
        echo "   ✅ {$key}: " . (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) . "\n";
    } else {
        echo "   ❌ {$key}: NOT SET\n";
    }
}

echo "\n";

// Test 2: Check config values
echo "2. Checking config() values...\n";

$configVars = [
    'midtrans.merchant_id' => config('midtrans.merchant_id'),
    'midtrans.client_key' => config('midtrans.client_key'),
    'midtrans.server_key' => config('midtrans.server_key'),
    'midtrans.is_production' => config('midtrans.is_production'),
];

foreach ($configVars as $key => $value) {
    if ($value) {
        echo "   ✅ {$key}: " . (is_string($value) && strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) . "\n";
    } else {
        echo "   ❌ {$key}: NOT SET\n";
    }
}

echo "\n";

// Test 3: Test Midtrans Config class
echo "3. Testing Midtrans Config class...\n";

try {
    // Set Midtrans configuration
    \Midtrans\Config::$serverKey = config('midtrans.server_key');
    \Midtrans\Config::$isProduction = config('midtrans.is_production');
    \Midtrans\Config::$isSanitized = config('midtrans.is_sanitized');
    \Midtrans\Config::$is3ds = config('midtrans.is_3ds');
    
    echo "   ✅ Midtrans Config set successfully\n";
    echo "   📊 Server Key: " . (strlen(\Midtrans\Config::$serverKey) > 0 ? 'SET' : 'NOT SET') . "\n";
    echo "   📊 Is Production: " . (\Midtrans\Config::$isProduction ? 'true' : 'false') . "\n";
    echo "   📊 Is Sanitized: " . (\Midtrans\Config::$isSanitized ? 'true' : 'false') . "\n";
    echo "   📊 Is 3DS: " . (\Midtrans\Config::$is3ds ? 'true' : 'false') . "\n";
    
} catch (\Exception $e) {
    echo "   ❌ Error setting Midtrans Config: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Test simple Snap token creation
echo "4. Testing Snap token creation...\n";

try {
    $params = [
        'transaction_details' => [
            'order_id' => 'TEST-' . time(),
            'gross_amount' => 50000,
        ],
        'item_details' => [
            [
                'id' => 'TEST-ITEM',
                'price' => 50000,
                'quantity' => 1,
                'name' => 'Test Ubi Bakar',
                'category' => 'Food'
            ]
        ],
        'customer_details' => [
            'first_name' => 'Test Customer',
            'phone' => '081234567890'
        ]
    ];
    
    echo "   🚀 Creating test Snap token...\n";
    $snapToken = \Midtrans\Snap::getSnapToken($params);
    
    if ($snapToken) {
        echo "   ✅ Snap token created successfully!\n";
        echo "   🎫 Token: " . substr($snapToken, 0, 30) . "...\n";
        
        $redirectUrl = 'https://app.sandbox.midtrans.com/snap/v2/vtweb/' . $snapToken;
        echo "   🌐 Redirect URL: " . $redirectUrl . "\n";
        
    } else {
        echo "   ❌ Failed to create Snap token\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error creating Snap token: " . $e->getMessage() . "\n";
    echo "   💡 This might be due to network issues or invalid credentials\n";
}

echo "\n";

echo "🏁 Configuration test completed!\n\n";

echo "💡 TROUBLESHOOTING:\n";
echo "===================\n";
echo "1. If config values are NOT SET:\n";
echo "   - Check .env file has correct values\n";
echo "   - Run: php artisan config:clear\n";
echo "   - Run: php artisan config:cache\n\n";

echo "2. If Snap token creation fails:\n";
echo "   - Verify Midtrans credentials are correct\n";
echo "   - Check internet connection\n";
echo "   - Verify Midtrans sandbox is accessible\n\n";

echo "3. If still getting ServerKey/ClientKey null error:\n";
echo "   - Clear all caches: php artisan cache:clear\n";
echo "   - Restart Laravel server\n";
echo "   - Check file permissions on config files\n\n";
