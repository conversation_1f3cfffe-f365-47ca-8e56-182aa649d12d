@extends('layouts.app')

@section('title', 'Distribusi Urgent - Produk Mendekati Kadaluarsa')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
            Distribusi Urgent - Produk Mendekati Kadaluarsa
        </h1>
        <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3 bg-warning">
                    <h6 class="m-0 font-weight-bold text-dark">
                        <i class="fas fa-truck me-2"></i>Form Distribusi Urgent
                    </h6>
                </div>
                <div class="card-body">
                    @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('expiry-recommendations.distribute.process', $item->id) }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="destination" class="form-label">Tujuan Distribusi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('destination') is-invalid @enderror" 
                                           id="destination" name="destination" value="{{ old('destination') }}" 
                                           list="market-suggestions" required>
                                    <datalist id="market-suggestions">
                                        @foreach($markets as $market)
                                            <option value="{{ $market }}">
                                        @endforeach
                                    </datalist>
                                    @error('destination')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="quantity" class="form-label">Jumlah Distribusi <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <input type="number" class="form-control @error('quantity') is-invalid @enderror" 
                                               id="quantity" name="quantity" value="{{ old('quantity', $item->current_stock) }}" 
                                               min="1" max="{{ $item->current_stock }}" required>
                                        <span class="input-group-text">/ {{ $item->current_stock }} tersedia</span>
                                    </div>
                                    @error('quantity')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="distribution_date" class="form-label">Tanggal Distribusi <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control @error('distribution_date') is-invalid @enderror" 
                                           id="distribution_date" name="distribution_date" 
                                           value="{{ old('distribution_date', date('Y-m-d')) }}" 
                                           min="{{ date('Y-m-d') }}" required>
                                    @error('distribution_date')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="vehicle_info" class="form-label">Informasi Kendaraan</label>
                                    <input type="text" class="form-control @error('vehicle_info') is-invalid @enderror" 
                                           id="vehicle_info" name="vehicle_info" value="{{ old('vehicle_info') }}" 
                                           placeholder="Contoh: Truck B 1234 XYZ">
                                    @error('vehicle_info')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="driver_name" class="form-label">Nama Sopir</label>
                                    <input type="text" class="form-control @error('driver_name') is-invalid @enderror" 
                                           id="driver_name" name="driver_name" value="{{ old('driver_name') }}">
                                    @error('driver_name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check mt-4">
                                        <input class="form-check-input" type="checkbox" id="urgent_distribution" 
                                               name="urgent_distribution" value="1" checked>
                                        <label class="form-check-label text-warning fw-bold" for="urgent_distribution">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            Distribusi Urgent (Prioritas Tinggi)
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="notes" class="form-label">Catatan</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" name="notes" rows="3" 
                                      placeholder="Catatan tambahan untuk distribusi urgent...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <hr>

                        <div class="d-flex justify-content-end">
                            <a href="{{ route('expiry-recommendations.index') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times"></i> Batal
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-truck"></i> Buat Distribusi Urgent
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3 bg-danger text-white">
                    <h6 class="m-0 font-weight-bold">
                        <i class="fas fa-exclamation-circle me-2"></i>Informasi Produk
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-4">
                            <strong>Produk:</strong>
                        </div>
                        <div class="col-8">
                            {{ $item->name }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-4">
                            <strong>Batch:</strong>
                        </div>
                        <div class="col-8">
                            {{ $item->batch_number }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-4">
                            <strong>Stok:</strong>
                        </div>
                        <div class="col-8">
                            <span class="badge bg-info">{{ $item->current_stock }} unit</span>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-4">
                            <strong>Kadaluarsa:</strong>
                        </div>
                        <div class="col-8">
                            @php
                                $daysUntilExpiry = \Carbon\Carbon::parse($item->expiry_date)->diffInDays(now(), false);
                                $isExpired = $daysUntilExpiry < 0;
                                $isNearExpiry = $daysUntilExpiry <= 3 && $daysUntilExpiry >= 0;
                            @endphp
                            
                            <div>{{ \Carbon\Carbon::parse($item->expiry_date)->format('d M Y') }}</div>
                            @if($isExpired)
                                <span class="badge bg-danger">Sudah Kadaluarsa</span>
                            @elseif($isNearExpiry)
                                <span class="badge bg-warning">{{ abs($daysUntilExpiry) }} hari lagi</span>
                            @else
                                <span class="badge bg-success">{{ abs($daysUntilExpiry) }} hari lagi</span>
                            @endif
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-4">
                            <strong>Harga:</strong>
                        </div>
                        <div class="col-8">
                            Rp {{ number_format($item->selling_price, 0, ',', '.') }}
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-4">
                            <strong>Total Nilai:</strong>
                        </div>
                        <div class="col-8">
                            <strong>Rp {{ number_format($item->current_stock * $item->selling_price, 0, ',', '.') }}</strong>
                        </div>
                    </div>

                    <hr>

                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Peringatan:</h6>
                        <ul class="mb-0">
                            <li>Produk ini mendekati tanggal kadaluarsa</li>
                            <li>Distribusi urgent diperlukan untuk menghindari kerugian</li>
                            <li>Prioritaskan distribusi ke pasar terdekat</li>
                            <li>Pastikan kendaraan dan sopir siap</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const quantityInput = document.getElementById('quantity');
    const maxStock = {{ $item->current_stock }};
    
    quantityInput.addEventListener('input', function() {
        const value = parseInt(this.value);
        if (value > maxStock) {
            this.value = maxStock;
            alert(`Jumlah maksimal yang dapat didistribusikan adalah ${maxStock} unit`);
        }
    });
    
    // Auto-suggest untuk destination berdasarkan input
    const destinationInput = document.getElementById('destination');
    destinationInput.addEventListener('input', function() {
        // Bisa ditambahkan logic untuk auto-complete dari API
    });
});
</script>
@endsection
