

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="page-title">
        <i class="fas fa-chart-line"></i>
        <span><PERSON><PERSON><PERSON></span>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <span>Filter Laporan</span>
                </div>
                <div class="card-body">
                    <form action="<?php echo e(route('reports.sales')); ?>" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="period" class="form-label">Periode</label>
                            <select class="form-select" id="period" name="period">
                                <option value="daily" <?php echo e(request('period') == 'daily' ? 'selected' : ''); ?>>Harian</option>
                                <option value="weekly" <?php echo e(request('period') == 'weekly' ? 'selected' : ''); ?>>Mingguan</option>
                                <option value="monthly" <?php echo e(request('period') == 'monthly' ? 'selected' : ''); ?>>Bulanan</option>
                                <option value="custom" <?php echo e(request('period') == 'custom' ? 'selected' : ''); ?>>Kustom</option>
                            </select>
                        </div>
                        
                        <div class="col-md-3 custom-date" style="<?php echo e(request('period') == 'custom' ? '' : 'display: none;'); ?>">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo e(request('start_date')); ?>">
                        </div>
                        
                        <div class="col-md-3 custom-date" style="<?php echo e(request('period') == 'custom' ? '' : 'display: none;'); ?>">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo e(request('end_date')); ?>">
                        </div>
                        
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="submit" class="btn btn-primary flex-fill">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <a href="<?php echo e(route('reports.sales')); ?>" class="btn btn-outline-secondary flex-fill">
                                    <i class="fas fa-sync"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon primary">
                    <i class="fas fa-cash-register"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Transaksi</div>
                    <h3 class="stats-value"><?php echo e($summary['total_transactions']); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon success">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Total Pendapatan</div>
                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['total_sales'], 0, ',', '.')); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="stats-card">
                <div class="stats-icon warning">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <div class="stats-info">
                    <div class="stats-title">Rata-rata per Transaksi</div>
                    <h3 class="stats-value">Rp <?php echo e(number_format($summary['average_transaction'], 0, ',', '.')); ?></h3>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <span>Grafik Penjualan <?php echo e($reportTitle); ?></span>
                </div>
                <div class="card-body">
                    <canvas id="salesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <span>Produk Terlaris</span>
                </div>
                <div class="card-body">
                    <canvas id="productChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>Detail Transaksi</span>
                    <a href="<?php echo e(route('reports.export', ['type' => 'sales', 'start_date' => request('start_date'), 'end_date' => request('end_date')])); ?>" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>Tanggal</th>
                                    <th>No. Invoice</th>
                                    <th>Jumlah Item</th>
                                    <th>Total</th>
                                    <th>Metode Pembayaran</th>
                                    <th>Kasir</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $transactions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $transaction): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($transaction->created_at->format('d M Y H:i')); ?></td>
                                    <td><?php echo e($transaction->invoice_number); ?></td>
                                    <td><?php echo e($transaction->items_count); ?> item</td>
                                    <td>Rp <?php echo e(number_format($transaction->total_amount, 0, ',', '.')); ?></td>
                                    <td><?php echo e($transaction->payment_method); ?></td>
                                    <td><?php echo e($transaction->user->name ?? 'Admin'); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('transactions.show', $transaction)); ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="7" class="text-center">Tidak ada data transaksi</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <div class="mt-4">
                        <?php echo e($transactions->appends(request()->query())->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Period selection toggling
        const periodSelect = document.getElementById('period');
        const customDateFields = document.querySelectorAll('.custom-date');
        
        periodSelect.addEventListener('change', function() {
            if (this.value === 'custom') {
                customDateFields.forEach(field => {
                    field.style.display = '';
                });
            } else {
                customDateFields.forEach(field => {
                    field.style.display = 'none';
                });
            }
        });
        
        // Sales Chart
        const salesChartData = <?php echo json_encode($salesChartData, 15, 512) ?>;
        const salesChart = new Chart(document.getElementById('salesChart'), {
            type: 'line',
            data: {
                labels: salesChartData.labels,
                datasets: [{
                    label: 'Penjualan',
                    data: salesChartData.data,
                    backgroundColor: 'rgba(139, 69, 19, 0.2)',
                    borderColor: 'rgba(139, 69, 19, 1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return 'Rp ' + value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Penjualan: Rp ' + context.parsed.y.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
                            }
                        }
                    }
                }
            }
        });
        
        // Product Chart
        const productChartData = <?php echo json_encode($topProductsChart, 15, 512) ?>;
        const productChart = new Chart(document.getElementById('productChart'), {
            type: 'doughnut',
            data: {
                labels: productChartData.labels,
                datasets: [{
                    data: productChartData.data,
                    backgroundColor: [
                        'rgba(139, 69, 19, 0.7)',
                        'rgba(255, 140, 0, 0.7)',
                        'rgba(210, 105, 30, 0.7)',
                        'rgba(160, 82, 45, 0.7)',
                        'rgba(205, 133, 63, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = Math.round((value / total) * 100);
                                return context.label + ': ' + percentage + '% (' + value + ' terjual)';
                            }
                        }
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\TUGAS AKHIR\WEBSITE\WEB\iterasi 2\ubi-bakar-cilembu\resources\views/reports/sales.blade.php ENDPATH**/ ?>