@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-chart-line me-2"></i>
            <span class="fw-bold">Laporan Proses Produksi</span>
        </div>
        <div class="button-group d-flex">
            <a href="{{ route('processed-inventory.index') }}" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> <PERSON><PERSON>li
            </a>
            <a href="{{ route('processed-inventory.show-process-form') }}" class="btn btn-success">
                <i class="fas fa-industry me-1"></i> Proses Produksi Baru
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-filter me-2"></i> Fi<PERSON></h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('production.reports') }}" method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Tanggal Mulai</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request('start_date') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">Tanggal Akhir</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request('end_date') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="processed_inventory_id" class="form-label">Produk</label>
                            <select class="form-select" id="processed_inventory_id" name="processed_inventory_id">
                                <option value="">Semua Produk</option>
                                @foreach($processedItems as $item)
                                <option value="{{ $item->id }}" {{ request('processed_inventory_id') == $item->id ? 'selected' : '' }}>
                                    {{ $item->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="raw_inventory_id" class="form-label">Bahan Baku</label>
                            <select class="form-select" id="raw_inventory_id" name="raw_inventory_id">
                                <option value="">Semua Bahan Baku</option>
                                @foreach($rawItems as $item)
                                <option value="{{ $item->id }}" {{ request('raw_inventory_id') == $item->id ? 'selected' : '' }}>
                                    {{ $item->name }}
                                </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> Cari
                            </button>
                            <a href="{{ route('production.reports') }}" class="btn btn-secondary">
                                <i class="fas fa-sync me-1"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5><i class="fas fa-list me-2"></i> Daftar Proses Produksi</h5>
                    <span class="badge bg-info">{{ $productionLogs->total() }} Proses Produksi</span>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Bahan Baku Digunakan</h5>
                                    <h3 class="mb-0">{{ number_format($totalRawAmount, 2) }} kg</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Produk Dihasilkan</h5>
                                    <h3 class="mb-0">{{ number_format($totalProducedAmount) }} item</h3>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">Total Biaya Produksi</h5>
                                    <h3 class="mb-0">Rp {{ number_format($totalCost, 0, ',', '.') }}</h3>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if(count($productionLogs) > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="table-dark">
                                <tr>
                                    <th>No</th>
                                    <th>Tanggal</th>
                                    <th>Bahan Baku</th>
                                    <th>Produk Hasil</th>
                                    <th>Jumlah Bahan (kg)</th>
                                    <th>Jumlah Hasil</th>
                                    <th>Biaya Bahan</th>
                                    <th>Biaya Tambahan</th>
                                    <th>Total Biaya</th>
                                    <th>Biaya per Item</th>
                                    <th>Oleh</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($productionLogs as $index => $log)
                                <tr>
                                    <td>{{ $productionLogs->firstItem() + $index }}</td>
                                    <td>{{ $log->created_at->format('d M Y H:i') }}</td>
                                    <td>{{ $log->raw_name }}</td>
                                    <td>{{ $log->processed_name }}</td>
                                    <td>{{ number_format($log->raw_amount_used, 2) }} kg</td>
                                    <td>{{ number_format($log->produced_amount) }} item</td>
                                    <td>Rp {{ number_format($log->raw_cost, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($log->additional_cost, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($log->total_cost, 0, ',', '.') }}</td>
                                    <td>Rp {{ number_format($log->cost_per_item, 0, ',', '.') }}</td>
                                    <td>{{ $log->user->name ?? 'Unknown' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="mt-4">
                        {{ $productionLogs->withQueryString()->links() }}
                    </div>
                    @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Tidak ada data proses produksi yang ditemukan.
                    </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .page-title {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .page-title .fw-bold {
        font-size: 1.25rem;
    }
    
    .button-group .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .button-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .btn-primary {
        background-color: #8B4513;
        border-color: #8B4513;
    }
    
    .btn-success {
        background-color: #4CAF50;
        border-color: #4CAF50;
    }
    
    .btn-info {
        background-color: #2196F3;
        border-color: #2196F3;
        color: white;
    }
    
    .btn-info:hover {
        color: white;
    }
    
    @media (max-width: 768px) {
        .page-title {
            flex-direction: column;
            align-items: flex-start !important;
        }
        
        .button-group {
            margin-top: 15px;
            width: 100%;
        }
        
        .button-group .btn {
            flex: 1;
            margin-bottom: 5px;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Set end date to today if not set and start date is set
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        
        if (startDateInput.value && !endDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            endDateInput.value = today;
        }
    });
</script>
@endpush
@endsection 