<?php

require_once 'vendor/autoload.php';

// Load Laravel environment
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🔧 FIXING ARRAY KEY ERROR\n";
echo "=========================\n\n";

// Fix 1: Clear Laravel caches
echo "1. Clearing Laravel caches...\n";

try {
    \Illuminate\Support\Facades\Artisan::call('cache:clear');
    echo "   ✅ Application cache cleared\n";
    
    \Illuminate\Support\Facades\Artisan::call('config:clear');
    echo "   ✅ Configuration cache cleared\n";
    
    \Illuminate\Support\Facades\Artisan::call('view:clear');
    echo "   ✅ View cache cleared\n";
    
    \Illuminate\Support\Facades\Artisan::call('route:clear');
    echo "   ✅ Route cache cleared\n";
    
} catch (\Exception $e) {
    echo "   ❌ Error clearing caches: " . $e->getMessage() . "\n";
}

echo "\n";

// Fix 2: Check and clean orphaned transaction items
echo "2. Checking for orphaned transaction items...\n";

try {
    // Find transaction items with invalid processed_inventory_id
    $orphanedProcessed = \App\Models\TransactionItem::whereNotNull('processed_inventory_id')
        ->whereDoesntHave('processedInventory')
        ->get();
        
    if ($orphanedProcessed->count() > 0) {
        echo "   ⚠️  Found {$orphanedProcessed->count()} orphaned processed inventory items\n";
        
        foreach ($orphanedProcessed as $item) {
            echo "      - Transaction Item ID: {$item->id}, Invalid Processed ID: {$item->processed_inventory_id}\n";
            
            // Try to find the product in OtherProduct
            $otherProduct = \App\Models\OtherProduct::find($item->product_id);
            if ($otherProduct) {
                echo "        → Found in OtherProduct, updating...\n";
                $item->update([
                    'processed_inventory_id' => null,
                    'product_id' => $otherProduct->id
                ]);
            } else {
                echo "        → Product not found anywhere, marking as invalid\n";
            }
        }
    } else {
        echo "   ✅ No orphaned processed inventory items found\n";
    }
    
    // Find transaction items with invalid product_id
    $orphanedOther = \App\Models\TransactionItem::whereNotNull('product_id')
        ->whereNull('processed_inventory_id')
        ->get()
        ->filter(function($item) {
            return !\App\Models\OtherProduct::find($item->product_id);
        });
        
    if ($orphanedOther->count() > 0) {
        echo "   ⚠️  Found {$orphanedOther->count()} orphaned other product items\n";
        
        foreach ($orphanedOther as $item) {
            echo "      - Transaction Item ID: {$item->id}, Invalid Product ID: {$item->product_id}\n";
        }
    } else {
        echo "   ✅ No orphaned other product items found\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking orphaned items: " . $e->getMessage() . "\n";
}

echo "\n";

// Fix 3: Validate current products
echo "3. Validating current products...\n";

try {
    $processedProducts = \App\Models\ProcessedInventory::where('is_active', true)->get();
    $otherProducts = \App\Models\OtherProduct::where('is_active', true)->get();
    
    echo "   📊 Active ProcessedInventory: {$processedProducts->count()}\n";
    echo "   📊 Active OtherProduct: {$otherProducts->count()}\n";
    
    // Check for products with invalid data
    $invalidProcessed = $processedProducts->filter(function($product) {
        return empty($product->name) || $product->selling_price <= 0;
    });
    
    $invalidOther = $otherProducts->filter(function($product) {
        return empty($product->name) || $product->selling_price <= 0;
    });
    
    if ($invalidProcessed->count() > 0) {
        echo "   ⚠️  Found {$invalidProcessed->count()} invalid processed products\n";
        foreach ($invalidProcessed as $product) {
            echo "      - ID: {$product->id}, Name: '{$product->name}', Price: {$product->selling_price}\n";
        }
    }
    
    if ($invalidOther->count() > 0) {
        echo "   ⚠️  Found {$invalidOther->count()} invalid other products\n";
        foreach ($invalidOther as $product) {
            echo "      - ID: {$product->id}, Name: '{$product->name}', Price: {$product->selling_price}\n";
        }
    }
    
    if ($invalidProcessed->count() == 0 && $invalidOther->count() == 0) {
        echo "   ✅ All products have valid data\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error validating products: " . $e->getMessage() . "\n";
}

echo "\n";

// Fix 4: Check recent transactions
echo "4. Checking recent transactions...\n";

try {
    $recentTransactions = \App\Models\Transaction::with('items')
        ->where('created_at', '>=', now()->subDays(1))
        ->orderBy('created_at', 'desc')
        ->get();
        
    echo "   📊 Recent transactions (last 24h): {$recentTransactions->count()}\n";
    
    $problematicTransactions = [];
    
    foreach ($recentTransactions as $transaction) {
        foreach ($transaction->items as $item) {
            $hasValidProduct = false;
            
            if ($item->processed_inventory_id) {
                $hasValidProduct = \App\Models\ProcessedInventory::find($item->processed_inventory_id) !== null;
            } elseif ($item->product_id) {
                $hasValidProduct = \App\Models\OtherProduct::find($item->product_id) !== null;
            }
            
            if (!$hasValidProduct) {
                $problematicTransactions[] = [
                    'transaction_id' => $transaction->id,
                    'item_id' => $item->id,
                    'product_id' => $item->product_id,
                    'processed_id' => $item->processed_inventory_id
                ];
            }
        }
    }
    
    if (count($problematicTransactions) > 0) {
        echo "   ⚠️  Found " . count($problematicTransactions) . " problematic transaction items\n";
        foreach ($problematicTransactions as $problem) {
            echo "      - Transaction: {$problem['transaction_id']}, Item: {$problem['item_id']}\n";
        }
    } else {
        echo "   ✅ All recent transactions have valid product references\n";
    }
    
} catch (\Exception $e) {
    echo "   ❌ Error checking transactions: " . $e->getMessage() . "\n";
}

echo "\n";

echo "🏁 Fix completed!\n\n";

echo "💡 RECOMMENDATIONS:\n";
echo "===================\n";
echo "1. ✅ Caches have been cleared\n";
echo "2. ✅ Data validation completed\n";
echo "3. 🔄 Restart Laravel server: php artisan serve\n";
echo "4. 🌐 Refresh browser and clear localStorage\n";
echo "5. 🛒 Start with fresh cart in POS\n\n";

echo "🚀 NEXT STEPS:\n";
echo "==============\n";
echo "1. Test payment gateway with simple transaction\n";
echo "2. Monitor Laravel logs for any new errors\n";
echo "3. If error persists, check browser console\n";
echo "4. Ensure all products have valid data\n\n";
