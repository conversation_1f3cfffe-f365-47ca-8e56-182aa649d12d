<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Traits\Auditable;

class Transaction extends Model
{
    use HasFactory, SoftDeletes, Auditable;

    protected $table = 'transactions';

    protected $fillable = [
        'invoice_number',
        'user_id',
        'customer_name',
        'customer_phone',
        'subtotal',
        'tax',
        'discount',
        'total_amount',
        'amount_paid',
        'change_amount',
        'payment_method',
        'status',
        'notes'
    ];

    protected static function booted()
    {
        static::creating(function ($transaction) {
            if (empty($transaction->invoice_number)) {
                // Format: INV/YYYYMMDD/XXXX
                $date = Carbon::now()->format('Ymd');
                $latestTransaction = static::whereDate('created_at', Carbon::today())->latest()->first();

                $sequence = $latestTransaction ? intval(substr($latestTransaction->invoice_number, -4)) + 1 : 1;
                $transaction->invoice_number = 'INV/' . $date . '/' . str_pad($sequence, 4, '0', STR_PAD_LEFT);
            }
        });
    }

    /**
     * Get the user that created the transaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the transaction items for this transaction.
     */
    public function items(): HasMany
    {
        return $this->hasMany(TransactionItem::class);
    }
}
