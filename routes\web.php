<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\EmployeeDashboardController;
use App\Http\Controllers\RawInventoryController;
use App\Http\Controllers\ProcessedInventoryController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\OtherProductController;
use App\Http\Controllers\FinancialProjectionController;
use App\Http\Controllers\ProductionController;
use App\Http\Controllers\DistributionController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\FinancialReportController;
use App\Http\Controllers\ExpiryRecommendationController;
use App\Http\Controllers\Admin\AuditLogController;

// Landing page
Route::get('/', function () {
    return redirect()->route('login');
});

// Info login untuk debugging
Route::get('/login-info', function () {
    return view('auth.login-info');
});

// Database debugging
Route::get('/check-users', function () {
    return view('auth.check-user');
});

// Debug routes
Route::get('/routes', function () {
    $routeCollection = \Illuminate\Support\Facades\Route::getRoutes();
    echo "<h1>Routes List</h1>";
    echo "<table border='1'>";
    echo "<tr>";
    echo "<td><b>Method</b></td>";
    echo "<td><b>URI</b></td>";
    echo "<td><b>Name</b></td>";
    echo "</tr>";

    foreach ($routeCollection as $value) {
        echo "<tr>";
        echo "<td>" . implode('|', $value->methods()) . "</td>";
        echo "<td>" . $value->uri() . "</td>";
        echo "<td>" . $value->getName() . "</td>";
        echo "</tr>";
    }
    echo "</table>";
});

// Route API untuk mendapatkan role yang valid
Route::get('/api/valid-roles', function() {
    try {
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $roleType = $roleInfo[0]->Type;
        $validRoles = [];

        if (strpos($roleType, 'enum') !== false) {
            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
            $validRoles = explode("','", $matches[1]);
        }

        return response()->json([
            'status' => 'success',
            'role_type' => $roleType,
            'valid_roles' => $validRoles
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
});

// Super emergency login (sangat sederhana)
Route::get('/login-simple', function() {
    return 'Halaman login sederhana';
});

// Route untuk membuat user karyawan secara langsung
Route::get('/create-karyawan', function() {
    try {
        // Cek apakah database terhubung
        $db_name = \Illuminate\Support\Facades\DB::connection()->getDatabaseName();

        // Dapatkan nilai valid untuk role
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $roleType = $roleInfo[0]->Type;
        $validRoles = [];

        if (strpos($roleType, 'enum') !== false) {
            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
            $validRoles = explode("','", $matches[1]);
        }

        // Tampilkan informasi tentang nilai yang valid untuk role
        echo "<h2>Nilai Role yang Valid:</h2>";
        echo "<ul>";
        foreach ($validRoles as $role) {
            echo "<li>{$role}</li>";
        }
        echo "</ul>";

        // Gunakan 'employee' untuk karyawan
        $roleToUse = 'employee';

        // Buat user karyawan baru
        $user = new \App\Models\User();
        $user->name = 'Karyawan Toko';
        $user->email = '<EMAIL>';
        $user->password = \Illuminate\Support\Facades\Hash::make('karyawan123');
        $user->role = $roleToUse;
        $user->save();

        return "<h1>User karyawan berhasil dibuat!</h1>
                <p><strong>Email:</strong> <EMAIL></p>
                <p><strong>Password:</strong> karyawan123</p>
                <p><strong>Role yang digunakan:</strong> {$roleToUse}</p>
                <p><strong>Database:</strong> {$db_name}</p>
                <p><a href='/emergency-login-karyawan'>Coba login sekarang</a></p>";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Route untuk memeriksa struktur tabel users
Route::get('/check-user-table', function() {
    try {
        // Cek struktur tabel users
        $columns = \Illuminate\Support\Facades\Schema::getColumnListing('users');

        echo "<h1>Struktur Tabel Users</h1>";
        echo "<table border='1'>";
        echo "<tr><th>Kolom</th><th>Tipe</th><th>Null</th><th>Default</th></tr>";

        // Dapatkan informasi kolom dari database
        $columnsInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users");

        foreach ($columnsInfo as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Default}</td>";
            echo "</tr>";
        }

        echo "</table>";

        // Khusus periksa kolom role
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        if (count($roleInfo) > 0) {
            $roleType = $roleInfo[0]->Type;
            echo "<h2>Informasi Kolom Role</h2>";
            echo "<p>Tipe: {$roleType}</p>";

            // Jika tipenya enum, tampilkan nilai-nilai yang valid
            if (strpos($roleType, 'enum') !== false) {
                preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
                $validValues = explode("','", $matches[1]);

                echo "<p>Nilai yang valid untuk kolom role:</p>";
                echo "<ul>";
                foreach ($validValues as $value) {
                    echo "<li>{$value}</li>";
                }
                echo "</ul>";
            }
        }

        return "";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Route untuk melihat semua user di database
Route::get('/show-users', function() {
    try {
        $users = \App\Models\User::all();
        echo "<h1>Daftar User</h1>";
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Nama</th><th>Email</th><th>Role</th></tr>";

        foreach ($users as $user) {
            echo "<tr>";
            echo "<td>{$user->id}</td>";
            echo "<td>{$user->name}</td>";
            echo "<td>{$user->email}</td>";
            echo "<td>{$user->role}</td>";
            echo "</tr>";
        }

        echo "</table>";
        echo "<p>Total user: " . count($users) . "</p>";

        // Tampilkan nama tabel users
        $table_name = (new \App\Models\User())->getTable();
        echo "<p>Nama tabel: {$table_name}</p>";

        // Tampilkan juga nama database
        $db_name = \Illuminate\Support\Facades\DB::connection()->getDatabaseName();
        echo "<p>Database: {$db_name}</p>";

    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Emergency login untuk karyawan (dengan info lengkap)
Route::get('/emergency-login-karyawan', function () {
    try {
        // Cek apakah tabel users ada
        $table_exists = \Illuminate\Support\Facades\Schema::hasTable('users');
        if (!$table_exists) {
            return 'Tabel users tidak ditemukan!';
        }

        // Cek jumlah user di database
        $users_count = \App\Models\User::count();

        // Cari user <NAME_EMAIL> tanpa melihat role
        $user = \App\Models\User::where('email', '<EMAIL>')->first();

        if ($user) {
            $userRole = $user->role;
            \Illuminate\Support\Facades\Auth::login($user);

            // Tampilkan info sebelum redirect
            return "
                <h1>Login berhasil!</h1>
                <p><strong>User:</strong> {$user->name}</p>
                <p><strong>Email:</strong> {$user->email}</p>
                <p><strong>Role:</strong> {$userRole}</p>
                <p>Mengalihkan ke dashboard dalam 3 detik...</p>
                <script>
                    setTimeout(function() {
                        window.location.href = '/home';
                    }, 3000);
                </script>
            ";
        }

        // Jika user tidak ditemukan, tampilkan info debugging
        return "
            <h1>User karyawan tidak ditemukan!</h1>
            <p>Total user di database: {$users_count}</p>
            <p>Silakan periksa dan buat user:</p>
            <ul>
                <li><a href='/check-user-table'>Periksa struktur tabel user</a></li>
                <li><a href='/create-karyawan'>Buat user karyawan baru</a></li>
                <li><a href='/show-users'>Lihat daftar semua user</a></li>
            </ul>
        ";
    } catch (\Exception $e) {
        return 'Error: ' . $e->getMessage();
    }
});

// Create test users
Route::post('/create-test-users', function () {
    // Create admin user
    App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Admin UBI',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'created_at' => now(),
            'updated_at' => now()
        ]
    );

    // Create karyawan user
    App\Models\User::updateOrCreate(
        ['email' => '<EMAIL>'],
        [
            'name' => 'Karyawan Toko',
            'password' => Hash::make('karyawan123'),
            'role' => 'employee',
            'created_at' => now(),
            'updated_at' => now()
        ]
    );

    return redirect('/check-users')->with('status', 'Test users created successfully!');
});

// Khusus login karyawan
Route::get('/login-karyawan', [App\Http\Controllers\KaryawanLoginController::class, 'showLoginForm'])->name('karyawan.login.form');
Route::post('/login-karyawan', [App\Http\Controllers\KaryawanLoginController::class, 'login'])->name('karyawan.login');

// Login langsung tanpa validasi role
Route::post('/login-direct', function(\Illuminate\Http\Request $request) {
    try {
        // Validasi input minimal
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        // Cari user berdasarkan email saja
        $user = \App\Models\User::where('email', $request->email)->first();

        if (!$user) {
            return back()->withErrors([
                'email' => 'Email tidak ditemukan di database.'
            ])->withInput();
        }

        // Cek password
        if (!\Illuminate\Support\Facades\Hash::check($request->password, $user->password)) {
            return back()->withErrors([
                'password' => 'Password salah.'
            ])->withInput();
        }

        // Login berhasil
        \Illuminate\Support\Facades\Auth::login($user, $request->boolean('remember'));

        // Informasi login berhasil
        $role = $user->role;
        $name = $user->name;

        // Redirect berdasarkan role
        if ($role === 'admin') {
            return redirect()->route('dashboard')
                ->with('success', "Selamat datang, {$name}! Anda berhasil login sebagai {$role}.");
        } else if ($role === 'employee' || $role === 'karyawan') {
            return redirect()->route('employee.dashboard')
                ->with('success', "Selamat datang, {$name}! Anda berhasil login sebagai Karyawan.");
        }

        // Default fallback
        return redirect()->route('employee.dashboard')
            ->with('success', "Selamat datang, {$name}! Anda berhasil login sebagai {$role}.");
    } catch (\Exception $e) {
        return back()->withErrors([
            'general' => 'Terjadi kesalahan: ' . $e->getMessage()
        ]);
    }
});

// Authentication routes
Auth::routes();

// Home routes (setelah login)
Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

// Routes yang bisa diakses oleh admin dan employee
Route::middleware(['auth'])->group(function () {
    // Dashboard routes
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/sales-report', [DashboardController::class, 'salesReport'])->name('dashboard.sales-report');
    Route::get('/dashboard/inventory-report', [DashboardController::class, 'inventoryReport'])->name('dashboard.inventory-report');

    // Inventory routes
    Route::prefix('inventory')->group(function () {
        Route::get('/', [InventoryController::class, 'index'])->name('inventory.index');
        Route::post('/raw', [InventoryController::class, 'addRawStock'])->name('inventory.add-raw');
        Route::post('/process', [InventoryController::class, 'processStock'])->name('inventory.process');
        Route::get('/low-stock-alert', [InventoryController::class, 'getLowStockAlert'])->name('inventory.low-stock-alert');
    });

    // Raw Inventory routes
    Route::resource('raw-inventory', RawInventoryController::class);
    Route::post('/raw-inventory/{rawInventory}/add-stock', [RawInventoryController::class, 'addStock'])->name('raw-inventory.add-stock');

    // Processed Inventory routes
    Route::resource('processed-inventory', ProcessedInventoryController::class);
    Route::get('/processed-inventory-process', [ProcessedInventoryController::class, 'showProcessForm'])->name('processed-inventory.show-process-form');
    Route::post('/processed-inventory-process', [ProcessedInventoryController::class, 'process'])->name('processed-inventory.process');
    Route::get('/production-reports', [ProcessedInventoryController::class, 'productionReports'])->name('production.reports');
    Route::get('/production-reports/export', [ProductionController::class, 'exportReports'])->name('production.reports.export');

    // Transaction routes
    Route::resource('transactions', TransactionController::class)->except(['edit', 'update']);
    Route::get('/transactions/{transaction}/receipt', [TransactionController::class, 'receipt'])->name('transactions.receipt');
    Route::get('/transactions/{transaction}/print', [TransactionController::class, 'receipt'])->name('transactions.print');
    Route::get('/transactions-export', [TransactionController::class, 'export'])->name('transactions.export');
    Route::post('/transactions/{transaction}/cancel', [TransactionController::class, 'cancel'])->name('transactions.cancel');
    Route::get('/pos', [TransactionController::class, 'create'])->name('pos');

    // Payment Gateway routes
    Route::prefix('payment')->group(function () {
        Route::post('/create/{transaction}', [App\Http\Controllers\PaymentController::class, 'createPayment'])->name('payment.create');
        Route::post('/notification', [App\Http\Controllers\PaymentController::class, 'handleNotification'])->name('payment.notification')->withoutMiddleware(['auth', 'verified']);
        Route::get('/status/{transaction}', [App\Http\Controllers\PaymentController::class, 'checkStatus'])->name('payment.status');
        Route::get('/finish', [App\Http\Controllers\PaymentController::class, 'paymentFinish'])->name('payment.finish');
        Route::get('/unfinish', [App\Http\Controllers\PaymentController::class, 'paymentUnfinish'])->name('payment.unfinish');
        Route::get('/error', [App\Http\Controllers\PaymentController::class, 'paymentError'])->name('payment.error');
    });

    // Other Products routes
    Route::resource('other-products', OtherProductController::class);

    // Route khusus untuk proyeksi keuangan yang dapat diakses oleh semua user
    Route::prefix('reports/financial-projection')->group(function () {
        Route::get('/', [FinancialProjectionController::class, 'index'])->name('reports.financial-projection');
        Route::get('/export-excel', [FinancialProjectionController::class, 'exportExcel'])->name('reports.financial-projection.export-excel');
        Route::get('/export-pdf', [FinancialProjectionController::class, 'exportPdf'])->name('reports.financial-projection.export-pdf');
    });
    
    // Route untuk rekomendasi ubi matang yang perlu segera dijual
    Route::prefix('expiry-recommendations')->group(function () {
        Route::get('/', [ExpiryRecommendationController::class, 'index'])->name('expiry-recommendations.index');
        Route::get('/update/{id}', [ExpiryRecommendationController::class, 'updateRecommendation'])->name('expiry-recommendations.update');
        Route::get('/update-all', [ExpiryRecommendationController::class, 'updateAllRecommendations'])->name('expiry-recommendations.update-all');
        Route::get('/export-excel', [ExpiryRecommendationController::class, 'exportExcel'])->name('expiry-recommendations.export-excel');
        Route::get('/export-pdf', [ExpiryRecommendationController::class, 'exportPdf'])->name('expiry-recommendations.export-pdf');
        Route::get('/sales-report', [ExpiryRecommendationController::class, 'salesReport'])->name('expiry-recommendations.sales-report');
        Route::post('/set-market/{id}', [ExpiryRecommendationController::class, 'setMarket'])->name('expiry-recommendations.set-market');
        Route::post('/create-distribution/{id}', [ExpiryRecommendationController::class, 'createDistribution'])->name('expiry-recommendations.create-distribution');
    });
});

// Routes khusus untuk Cashier (Kasir)
Route::middleware(['auth', 'role:cashier'])->group(function () {
    Route::get('/cashier/dashboard', [EmployeeDashboardController::class, 'index'])->name('cashier.dashboard');
    Route::get('/cashier/pos', [TransactionController::class, 'create'])->name('cashier.pos');
    Route::get('/cashier/transactions', [TransactionController::class, 'index'])->name('cashier.transactions');
});

// Routes khusus untuk Warehouse (Gudang)
Route::middleware(['auth', 'role:warehouse'])->group(function () {
    Route::get('/warehouse/dashboard', [EmployeeDashboardController::class, 'index'])->name('warehouse.dashboard');
    
    // Production Process Routes (hanya dapat diakses oleh Admin dan Warehouse)
    Route::resource('production', ProductionController::class);
    Route::get('/production/report', [ProductionController::class, 'report'])->name('production.report');
    
    // Distribution Routes (hanya dapat diakses oleh Admin dan Warehouse)
    Route::resource('distributions', DistributionController::class);
    Route::get('/distributions/report', [DistributionController::class, 'report'])->name('distributions.report');
    Route::post('/distributions/{distribution}/update-status', [DistributionController::class, 'updateStatus'])->name('distributions.update-status');
});

// Routes khusus untuk employee/karyawan lama (backward compatibility)
Route::middleware(['auth', \App\Http\Middleware\EmployeeMiddleware::class])->group(function () {
    Route::get('/employee-dashboard', [EmployeeDashboardController::class, 'index'])->name('employee.dashboard');
});

// Routes yang hanya bisa diakses oleh admin
Route::middleware(['auth', \App\Http\Middleware\AdminMiddleware::class])->group(function () {
    // Production Process Routes (admin juga bisa akses)
    Route::resource('production', ProductionController::class);
    Route::get('/production/report', [ProductionController::class, 'report'])->name('production.report');
    
    // Distribution Routes (admin juga bisa akses)
    Route::resource('distributions', DistributionController::class);
    Route::get('/distributions/report', [DistributionController::class, 'report'])->name('distributions.report');
    Route::post('/distributions/{distribution}/update-status', [DistributionController::class, 'updateStatus'])->name('distributions.update-status');
    
    // Expense Routes
    Route::resource('expense', ExpenseController::class);
    Route::get('/expense/report', [ExpenseController::class, 'report'])->name('expense.report');
    
    // Financial Reports
    Route::get('/financial', [FinancialReportController::class, 'index'])->name('financial.index');
    Route::get('/financial/income-statement', [FinancialReportController::class, 'incomeStatement'])->name('financial.income-statement');
    Route::get('/financial/inventory-valuation', [FinancialReportController::class, 'inventoryValuation'])->name('financial.inventory-valuation');
    Route::get('/financial/projection', [FinancialReportController::class, 'projection'])->name('financial.projection');
    
    // Reports (legacy - dipertahankan untuk kompatibilitas)
    Route::get('/reports/sales', [DashboardController::class, 'salesReport'])->name('reports.sales');
    Route::get('/reports/inventory', [DashboardController::class, 'inventoryReport'])->name('reports.inventory');
    Route::get('/reports/financial', [DashboardController::class, 'financialReport'])->name('reports.financial');
    Route::get('/reports/export/{type}', [DashboardController::class, 'exportReport'])->name('reports.export');

    // Daily sales report
    Route::get('/reports/daily', [TransactionController::class, 'dailyReport'])->name('reports.daily');

    // Audit Logs
    Route::get('/admin/audit-logs', [AuditLogController::class, 'index'])->name('admin.audit-logs.index');
    Route::get('/admin/audit-logs/{auditLog}', [AuditLogController::class, 'show'])->name('admin.audit-logs.show');
});

// Fix role route - automatically update role values to match the expected enum
Route::get('/fix-role-values', function() {
    try {
        // Check what are the valid values for role in the database
        $roleInfo = \Illuminate\Support\Facades\DB::select("SHOW COLUMNS FROM users WHERE Field = 'role'");
        $roleType = $roleInfo[0]->Type;
        $validRoles = [];

        if (strpos($roleType, 'enum') !== false) {
            preg_match("/^enum\(\'(.*)\'\)$/", $roleType, $matches);
            $validRoles = explode("','", $matches[1]);
        }

        echo "<h1>Fixing Role Values</h1>";
        echo "<p>Valid role values in database: " . implode(', ', $validRoles) . "</p>";

        // Check users with role 'employee'
        $employeeUsers = \App\Models\User::where('role', 'employee')->get();
        $updatedEmployeeCount = 0;

        // If 'karyawan' is a valid role but 'employee' is not, update those users
        if (in_array('karyawan', $validRoles) && !in_array('employee', $validRoles)) {
            foreach($employeeUsers as $user) {
                $user->role = 'karyawan';
                $user->save();
                $updatedEmployeeCount++;
            }
            echo "<p>Updated {$updatedEmployeeCount} users from 'employee' to 'karyawan'</p>";
        }

        // Check users with role 'karyawan'
        $karyawanUsers = \App\Models\User::where('role', 'karyawan')->get();
        $updatedKaryawanCount = 0;

        // If 'employee' is a valid role but 'karyawan' is not, update those users
        if (in_array('employee', $validRoles) && !in_array('karyawan', $validRoles)) {
            foreach($karyawanUsers as $user) {
                $user->role = 'employee';
                $user->save();
                $updatedKaryawanCount++;
            }
            echo "<p>Updated {$updatedKaryawanCount} users from 'karyawan' to 'employee'</p>";
        }

        // Create a new karyawan user with correct role
        $role = in_array('karyawan', $validRoles) ? 'karyawan' : 'employee';
        \App\Models\User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Karyawan Toko',
                'password' => \Illuminate\Support\Facades\Hash::make('karyawan123'),
                'role' => $role,
                'created_at' => now(),
                'updated_at' => now()
            ]
        );

        echo "<p>Created/updated karyawan user with role: {$role}</p>";
        echo "<p>Login credentials:</p>";
        echo "<ul>";
        echo "<li>Email: <EMAIL></li>";
        echo "<li>Password: karyawan123</li>";
        echo "</ul>";

        return "<p><a href='/emergency-login-karyawan' class='btn btn-success'>Try Login Now</a></p>";
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});
