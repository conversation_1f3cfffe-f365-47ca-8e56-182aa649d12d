<?php

namespace App\Http\Controllers;

use App\Models\ProcessedInventory;
use App\Models\RawInventory;
use App\Models\Transaction;
use App\Models\TransactionItem;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    /**
     * Create a new controller instance.
     */
    public function __construct()
    {
        $this->middleware('auth');
        // Hapus middleware admin supaya semua role dapat mengakses dashboard
    }

    /**
     * Show the dashboard.
     */
    public function index()
    {
        // Data untuk grafik penjualan bulanan
        $monthlyData = Transaction::select(
            DB::raw('SUM(total_amount) as total'),
            DB::raw("DATE_FORMAT(created_at, '%Y-%m') as month"),
            DB::raw("DATE_FORMAT(created_at, '%b %Y') as month_name")
        )
            ->where('status', 'completed')
            ->whereYear('created_at', date('Y'))
            ->groupBy('month', 'month_name')
            ->orderBy('month')
            ->get();

        // Data untuk grafik penjualan harian (30 hari terakhir)
        $dailyData = Transaction::select(
            DB::raw('SUM(total_amount) as total'),
            DB::raw('DATE(created_at) as date')
        )
            ->where('status', 'completed')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Ringkasan hari ini
        $today = Carbon::today();
        $todaySales = Transaction::whereDate('created_at', $today)
            ->where('status', 'completed')
            ->sum('total_amount');
        $todayTransactions = Transaction::whereDate('created_at', $today)
            ->where('status', 'completed')
            ->count();

        // Stok yang hampir habis
        $lowStockRaw = RawInventory::whereRaw('current_stock <= min_stock_threshold')->get();
        $lowStockProcessed = ProcessedInventory::whereRaw('current_stock <= min_stock_threshold')->get();

        // Get recent transactions for today
        $recentTransactions = Transaction::whereDate('created_at', $today)
                                ->orderBy('created_at', 'desc')
                                ->limit(5)
            ->get();

        // Get top selling products for the last 30 days
        $topProducts = TransactionItem::whereHas('transaction', function($query) {
                            $query->where('created_at', '>=', now()->subDays(30))
                                 ->where('status', 'completed');
                        })
                        ->select('product_id', DB::raw('SUM(quantity) as total_sold'))
                        ->with(['product:id,name'])
                        ->groupBy('product_id')
                        ->orderByDesc('total_sold')
                        ->limit(5)
            ->get();

        // Membuat kombinasi lowStockItems untuk view
        $lowStockItems = collect();
        foreach ($lowStockRaw as $item) {
            $lowStockItems->push((object)[
                'id' => $item->id,
                'name' => $item->name,
                'current_stock' => $item->current_stock,
                'min_stock_threshold' => $item->min_stock_threshold,
                'type' => 'raw'
            ]);
        }

        foreach ($lowStockProcessed as $item) {
            $lowStockItems->push((object)[
                'id' => $item->id,
                'name' => $item->name,
                'current_stock' => $item->current_stock,
                'min_stock_threshold' => $item->min_stock_threshold,
                'type' => 'processed'
            ]);
        }

        // Laporan laba/rugi sederhana untuk bulan ini
        $financialReport = $this->getFinancialReport();

        // Menyiapkan data untuk chart
        $lastWeek = collect();
        for($i = 6; $i >= 0; $i--) {
            $date = Carbon::today()->subDays($i);
            $lastWeek->push([
                'date' => $date->format('Y-m-d'),
                'label' => $date->format('D'),
                'revenue' => 0,
                'transactions' => 0
            ]);
        }

        // Mengisi data penjualan 7 hari terakhir
        $weeklyTransactions = Transaction::where('created_at', '>=', Carbon::today()->subDays(7))
                                    ->where('status', 'completed')
                                    ->select(
                                        DB::raw('DATE(created_at) as date'),
                                        DB::raw('SUM(total_amount) as revenue'),
                                        DB::raw('COUNT(*) as transaction_count')
                                    )
                                    ->groupBy('date')
                                    ->get();

        $lastWeek = $lastWeek->map(function($item) use ($weeklyTransactions) {
            $dayData = $weeklyTransactions->firstWhere('date', $item['date']);
            if ($dayData) {
                $item['revenue'] = $dayData->revenue;
                $item['transactions'] = $dayData->transaction_count;
            }
            return $item;
        });

        $salesChartData = [
            'labels' => $lastWeek->pluck('label')->toArray(),
            'revenue' => $lastWeek->pluck('revenue')->toArray(),
            'transactions' => $lastWeek->pluck('transactions')->toArray()
        ];

        // Calculating raw inventory totals
        $rawInventoryTotal = RawInventory::sum('current_stock');
        $rawInventoryCount = RawInventory::count();

        // Preparing summary data for dashboard
        $summary = [
            'today_sales_count' => $todayTransactions,
            'today_sales_amount' => $todaySales,
            'month_revenue' => $financialReport['revenue'],
            'month_revenue_trend' => '↑ ' . number_format(($financialReport['revenue'] > 0 ? 5.2 : 0), 1) . '%',
            'raw_inventory_total' => $rawInventoryTotal,
            'raw_inventory_count' => $rawInventoryCount,
            'low_stock_count' => $lowStockItems->count()
        ];

        return view('dashboard.index', compact(
            'monthlyData',
            'dailyData',
            'summary',
            'lowStockItems',
            'recentTransactions',
            'topProducts',
            'financialReport',
            'salesChartData'
        ));
    }

    /**
     * Get financial report for current month
     */
    private function getFinancialReport()
    {
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now();

        // Revenue
        $revenue = Transaction::whereBetween('created_at', [$startDate, $endDate])
                             ->where('status', 'completed')
                             ->sum('total_amount');

        // Raw material costs based on transaction items' cost_per_item
        $rawMaterialCost = DB::table('transaction_items')
                            ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
                            ->join('processed_inventory', 'transaction_items.product_id', '=', 'processed_inventory.id')
                            ->whereBetween('transactions.created_at', [$startDate, $endDate])
                            ->where('transactions.status', 'completed')
                            ->whereNotNull('processed_inventory.cost_per_item')
                            ->sum(DB::raw('transaction_items.quantity * processed_inventory.cost_per_item'));

        // Gross profit
        $grossProfit = $revenue - $rawMaterialCost;

        return [
            'revenue' => $revenue,
            'raw_material_cost' => $rawMaterialCost,
            'gross_profit' => $grossProfit,
            'period' => Carbon::now()->format('F Y')
        ];
    }

    /**
     * Show sales report.
     */
    public function salesReport(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->format('Y-m-d'));

        // Mengambil seluruh transaksi untuk perhitungan summary
        $allTransactions = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                                  ->where('status', 'completed')
                                  ->get();

        // Mengambil transaksi dengan pagination untuk ditampilkan di tabel
        $transactions = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                                  ->where('status', 'completed')
                                  ->with('items.product', 'user')
                                  ->orderBy('created_at', 'desc')
                                  ->paginate(10);

        // Calculate summary
        $totalSales = $allTransactions->sum('total_amount');
        $totalTransactions = $allTransactions->count();

        $summary = [
            'total_sales' => $totalSales,
            'total_transactions' => $totalTransactions,
            'average_transaction' => $totalTransactions > 0 ? $totalSales / $totalTransactions : 0,
            'payment_methods' => $allTransactions->groupBy('payment_method')
                                            ->map(function ($group) {
                                                return [
                                                    'count' => $group->count(),
                                                    'total' => $group->sum('total_amount')
                                                ];
                                            })
        ];

        // Top selling products
        $topProducts = TransactionItem::whereHas('transaction', function ($query) use ($startDate, $endDate) {
                                      $query->whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                                           ->where('status', 'completed');
                                  })
                                  ->with('product')
                                  ->select('product_id', DB::raw('SUM(quantity) as total_qty'), DB::raw('SUM(subtotal) as total_revenue'))
                                  ->groupBy('product_id')
                                  ->orderByDesc('total_qty')
                                  ->limit(10)
                                  ->get();

        // Menambahkan reportTitle untuk template
        $reportTitle = 'Periode ' . Carbon::parse($startDate)->format('d M Y') . ' - ' . Carbon::parse($endDate)->format('d M Y');

        // Menyiapkan chart data
        $salesChartData = [];
        $salesData = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                                ->where('status', 'completed')
                                ->select(
                                   DB::raw('DATE(created_at) as date'),
                                   DB::raw('SUM(total_amount) as revenue')
                                )
                                ->groupBy('date')
                                ->orderBy('date')
            ->get();

        $salesChartData = [
            'labels' => $salesData->pluck('date')->map(function($date) {
                return Carbon::parse($date)->format('d M');
            })->toArray(),
            'data' => $salesData->pluck('revenue')->toArray()
        ];

        // Menyiapkan top products
        $topProductsChart = [
            'labels' => $topProducts->pluck('product.name')->toArray(),
            'data' => $topProducts->pluck('total_qty')->toArray()
        ];

        return view('reports.sales', compact(
            'transactions',
            'summary',
            'topProducts',
            'startDate',
            'endDate',
            'reportTitle',
            'salesChartData',
            'topProductsChart'
        ));
    }

    /**
     * Show inventory report.
     */
    public function inventoryReport()
    {
        $rawInventory = RawInventory::orderBy('name')->get();
        $processedInventory = ProcessedInventory::with('rawMaterial')->orderBy('name')->get();

        // Calculate total values
        $rawValue = $rawInventory->sum(function($item) {
            return $item->current_stock * $item->cost_per_kg;
        });

        $processedValue = $processedInventory->sum(function($item) {
            return $item->current_stock * $item->selling_price;
        });

        // Count items with low stock
        $lowStockRaw = $rawInventory->where('current_stock', '<=', function($item) {
            return $item->min_stock_threshold ?? 0;
        })->count();

        $lowStockProcessed = $processedInventory->where('current_stock', '<=', function($item) {
            return $item->min_stock_threshold ?? 0;
        })->count();

        // Prepare low stock items for view
        $lowStock = collect();
        foreach ($rawInventory as $item) {
            if ($item->current_stock <= ($item->min_stock_threshold ?? 0)) {
                $lowStock->push((object)[
                    'id' => $item->id,
                    'name' => $item->name,
                    'current_stock' => $item->current_stock,
                    'min_stock_threshold' => $item->min_stock_threshold ?? 0,
                    'type' => 'raw'
                ]);
            }
        }

        foreach ($processedInventory as $item) {
            if ($item->current_stock <= ($item->min_stock_threshold ?? 0)) {
                $lowStock->push((object)[
                    'id' => $item->id,
                    'name' => $item->name,
                    'current_stock' => $item->current_stock,
                    'min_stock_threshold' => $item->min_stock_threshold ?? 0,
                    'type' => 'processed'
                ]);
            }
        }

        // Prepare summary data
        $summary = [
            'raw_count' => $rawInventory->count(),
            'processed_count' => $processedInventory->count(),
            'raw_total_stock' => $rawInventory->sum('current_stock'),
            'processed_total_stock' => $processedInventory->sum('current_stock'),
            'raw_total_value' => $rawValue,
            'processed_total_value' => $processedValue,
            'total_inventory_value' => $rawValue + $processedValue,
            'low_stock_raw' => $lowStockRaw,
            'low_stock_processed' => $lowStockProcessed,
            'low_stock_count' => $lowStock->count()
        ];

        // Prepare chart data
        $valueChartData = [
            'labels' => ['Bahan Mentah', 'Produk Jadi'],
            'data' => [$rawValue, $processedValue]
        ];

        $stockStatusData = [
            'labels' => ['Bahan Mentah', 'Produk Jadi'],
            'available' => [
                $rawInventory->count() - $lowStockRaw,
                $processedInventory->count() - $lowStockProcessed
            ],
            'low' => [$lowStockRaw, $lowStockProcessed]
        ];

        // Movement history (would require a separate table in production)
        $recentMovements = Transaction::with('items.product', 'user')
                                    ->latest()
                                    ->limit(10)
                                    ->get()
                                    ->map(function($transaction) {
                                        return [
                                            'date' => $transaction->created_at,
                                            'type' => 'Penjualan',
                                            'items' => $transaction->items->map(function($item) {
                                                return [
                                                    'name' => $item->product->name ?? 'Unknown',
                                                    'quantity' => $item->quantity
                                                ];
                                            }),
                                            'user' => $transaction->user->name ?? 'Unknown'
                                        ];
                                    });

        return view('reports.inventory', compact(
            'rawInventory',
            'processedInventory',
            'summary',
            'recentMovements',
            'lowStock',
            'valueChartData',
            'stockStatusData'
        ));
    }

    /**
     * Show financial report.
     */
    public function financialReport(Request $request)
    {
        // Set periode berdasarkan parameter
        $period = $request->input('period', 'monthly');

        // Tentukan tanggal mulai dan akhir berdasarkan periode
        $startDate = $request->input('start_date');
        $endDate = $request->input('end_date');

        if (!$startDate || !$endDate) {
            switch ($period) {
                case 'weekly':
                    $startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                    break;
                case 'monthly':
                    $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                    break;
                case 'yearly':
                    $startDate = Carbon::now()->startOfYear()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfYear()->format('Y-m-d');
                    break;
                default:
                    $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $endDate = Carbon::now()->format('Y-m-d');
            }
        }

        // Set reportTitle
        $reportTitle = 'Periode ' . Carbon::parse($startDate)->format('d M Y') . ' - ' . Carbon::parse($endDate)->format('d M Y');

        // 1. PENDAPATAN
        // =================================================

        // Revenue dari transaksi penjualan
        $salesRevenue = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->where('status', 'completed')
            ->sum('total_amount');

        // Pendapatan berdasarkan tipe produk
        $revenueByProductType = TransactionItem::join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
            ->leftJoin('processed_inventory', 'transaction_items.processed_inventory_id', '=', 'processed_inventory.id')
            ->whereBetween(DB::raw('DATE(transactions.created_at)'), [$startDate, $endDate])
            ->where('transactions.status', 'completed')
            ->select(
                DB::raw('COALESCE(processed_inventory.product_type, "Produk Lain") as product_type'),
                DB::raw('SUM(transaction_items.subtotal) as total_revenue')
            )
            ->groupBy('product_type')
            ->get()
            ->pluck('total_revenue', 'product_type')
            ->toArray();

        // Total pendapatan
        $totalRevenue = $salesRevenue;

        // 2. BIAYA (COGS dan OPERASIONAL)
        // =================================================

        // Cost of Goods Sold (COGS) dari produk yang terjual
        // Untuk produk olahan (ubi bakar)
        $processedCogs = DB::table('transaction_items')
            ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
            ->join('processed_inventory', 'transaction_items.processed_inventory_id', '=', 'processed_inventory.id')
            ->whereBetween(DB::raw('DATE(transactions.created_at)'), [$startDate, $endDate])
            ->where('transactions.status', 'completed')
            ->whereNotNull('processed_inventory.cost_per_item')
            ->sum(DB::raw('transaction_items.quantity * processed_inventory.cost_per_item'));

        // Untuk produk lain - estimasi COGS sebagai persentase dari harga jual
        $otherProductsRevenue = DB::table('transaction_items')
            ->join('transactions', 'transaction_items.transaction_id', '=', 'transactions.id')
            ->whereNull('transaction_items.processed_inventory_id')
            ->whereBetween(DB::raw('DATE(transactions.created_at)'), [$startDate, $endDate])
            ->where('transactions.status', 'completed')
            ->sum('transaction_items.subtotal');

        $otherProductsCogs = $otherProductsRevenue * 0.6; // Estimasi COGS 60% dari harga jual

        // Total COGS
        $totalCogs = $processedCogs + $otherProductsCogs;

        // Biaya bahan baku (estimasi 70% dari COGS)
        $rawMaterialCost = $totalCogs * 0.7;

        // Biaya produksi (estimasi 30% dari COGS)
        $productionCost = $totalCogs * 0.3;

        // Laba kotor
        $grossProfit = $totalRevenue - $totalCogs;
        $grossMargin = $totalRevenue > 0 ? ($grossProfit / $totalRevenue) * 100 : 0;

        // Biaya operasional berdasarkan persentase dari revenue
        $salaryExpense = $totalRevenue * 0.12; // Gaji karyawan (12% dari pendapatan)
        $rentExpense = $totalRevenue * 0.05;  // Sewa tempat (5% dari pendapatan)
        $utilityExpense = $totalRevenue * 0.02; // Utilitas - listrik, air, dll (2% dari pendapatan)
        $marketingExpense = $totalRevenue * 0.03; // Pemasaran (3% dari pendapatan)
        $otherExpense = $totalRevenue * 0.01; // Biaya lain-lain (1% dari pendapatan)

        // Total biaya operasional
        $totalOperatingExpense = $salaryExpense + $rentExpense + $utilityExpense + $marketingExpense + $otherExpense;

        // Laba operasional (EBIT - Earnings Before Interest and Taxes)
        $operatingProfit = $grossProfit - $totalOperatingExpense;

        // Biaya bunga (jika ada pinjaman/kredit)
        $interestExpense = 0; // Bisa disesuaikan jika ada pinjaman

        // Laba sebelum pajak
        $profitBeforeTax = $operatingProfit - $interestExpense;

        // Pajak penghasilan (asumsi 10% dari laba sebelum pajak)
        $taxRate = 10; // dalam persen
        $taxAmount = $profitBeforeTax > 0 ? ($profitBeforeTax * $taxRate / 100) : 0;

        // Laba bersih setelah pajak
        $netProfit = $profitBeforeTax - $taxAmount;
        $netProfitMargin = $totalRevenue > 0 ? ($netProfit / $totalRevenue) * 100 : 0;

        // 3. PERBANDINGAN DENGAN PERIODE SEBELUMNYA
        // =================================================

        // Tentukan periode sebelumnya
        $previousStartDate = Carbon::parse($startDate)->subDays(Carbon::parse($startDate)->diffInDays(Carbon::parse($endDate)) + 1)->format('Y-m-d');
        $previousEndDate = Carbon::parse($startDate)->subDay()->format('Y-m-d');

        // Revenue periode sebelumnya
        $previousRevenue = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$previousStartDate, $previousEndDate])
            ->where('status', 'completed')
            ->sum('total_amount');

        // COGS periode sebelumnya (estimasi)
        $previousCogs = $previousRevenue * 0.65; // Estimasi COGS 65% dari revenue

        // Profit periode sebelumnya
        $previousGrossProfit = $previousRevenue - $previousCogs;
        $previousOperatingExpense = $previousRevenue * 0.23; // Estimasi biaya operasional 23% dari revenue
        $previousNetProfit = $previousGrossProfit - $previousOperatingExpense - ($previousGrossProfit - $previousOperatingExpense) * 0.1;

        // Persentase perubahan
        $revenueChange = $previousRevenue > 0 ? (($totalRevenue - $previousRevenue) / $previousRevenue) * 100 : 0;
        $profitChange = $previousNetProfit > 0 ? (($netProfit - $previousNetProfit) / $previousNetProfit) * 100 : 0;

        // 4. DATA UNTUK GRAFIK DAN VISUALISASI
        // =================================================

        // Daily breakdown untuk chart
        $dailyBreakdown = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
            ->where('status', 'completed')
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('SUM(total_amount) as revenue'),
                DB::raw('COUNT(*) as transaction_count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Membuat data untuk chart
        $revExpChartData = [
            'labels' => $dailyBreakdown->pluck('date')->map(function($date) {
                return Carbon::parse($date)->format('d M');
            })->toArray(),
            'revenue' => $dailyBreakdown->pluck('revenue')->toArray(),
            'expense' => [],
            'profit' => []
        ];

        // Estimasi biaya dan profit per hari
        foreach ($dailyBreakdown as $index => $day) {
            $dayExpense = $day->revenue * ($totalCogs / $totalRevenue);
            $dayOperatingExpense = $day->revenue * ($totalOperatingExpense / $totalRevenue);
            $totalDayExpense = $dayExpense + $dayOperatingExpense;

            $revExpChartData['expense'][] = round($totalDayExpense);
            $revExpChartData['profit'][] = round($day->revenue - $totalDayExpense);
        }

        // Distribusi biaya
        $costDistributionData = [
            'labels' => ['Bahan Baku', 'Produksi', 'Gaji', 'Sewa', 'Utilitas', 'Pemasaran', 'Lainnya'],
            'data' => [
                round($rawMaterialCost),
                round($productionCost),
                round($salaryExpense),
                round($rentExpense),
                round($utilityExpense),
                round($marketingExpense),
                round($otherExpense)
            ]
        ];

        // Distribusi pendapatan berdasarkan jenis produk
        $revenueDistributionData = [
            'labels' => array_keys($revenueByProductType),
            'data' => array_values($revenueByProductType)
        ];

        // 5. RINGKASAN & DETAIL UNTUK VIEW
        // =================================================

        // Variabel $summary untuk statistik ringkasan
        $summary = [
            'total_revenue' => $totalRevenue,
            'total_cost' => $totalCogs + $totalOperatingExpense,
            'net_profit' => $netProfit,
            'revenue_change' => $revenueChange,
            'profit_change' => $profitChange
        ];

        // Variabel $details untuk laporan terperinci
        $details = [
            'sales_revenue' => $totalRevenue,
            'other_revenue' => 0, // Bisa ditambahkan jika ada pendapatan dari sumber lain
            'raw_material_cost' => $rawMaterialCost,
            'production_cost' => $productionCost,
            'gross_profit' => $grossProfit,
            'gross_margin' => $grossMargin,
            'salary_expense' => $salaryExpense,
            'rent_expense' => $rentExpense,
            'utility_expense' => $utilityExpense,
            'marketing_expense' => $marketingExpense,
            'other_expense' => $otherExpense,
            'total_operating_expense' => $totalOperatingExpense,
            'operating_profit' => $operatingProfit,
            'interest_expense' => $interestExpense,
            'profit_before_tax' => $profitBeforeTax,
            'tax_rate' => $taxRate,
            'tax' => $taxAmount,
            'net_profit' => $netProfit,
            'net_profit_margin' => $netProfitMargin
        ];

        return view('reports.financial', compact(
            'period',
            'startDate',
            'endDate',
            'reportTitle',
            'summary',
            'details',
            'previousRevenue',
            'previousNetProfit',
            'revenueChange',
            'profitChange',
            'revExpChartData',
            'costDistributionData',
            'revenueDistributionData',
            'revenueByProductType'
        ));
    }

    /**
     * Export report to PDF or Excel.
     */
    public function exportReport($type, Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));

        // Logic for different report types: sales, inventory, financial
        switch ($type) {
            case 'sales':
                $transactions = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                    ->orderBy('created_at', 'desc')
                    ->get();

                // Export logic would go here
                // For now, we'll just return the view
                return view('dashboard.reports.sales_export', compact('transactions', 'startDate', 'endDate'));

            case 'inventory':
                $rawInventory = RawInventory::where('is_active', true)->get();
                $processedInventory = ProcessedInventory::where('is_active', true)->get();

                return view('dashboard.reports.inventory_export', compact('rawInventory', 'processedInventory'));

            case 'financial':
                // Similar to financial report
                $revenue = Transaction::whereBetween(DB::raw('DATE(created_at)'), [$startDate, $endDate])
                    ->where('status', 'completed')
                    ->sum('total_amount');

                // More financial calculations...

                return view('dashboard.reports.financial_export', compact('revenue', 'startDate', 'endDate'));

            default:
                return redirect()->back()->with('error', 'Tipe laporan tidak valid');
        }
    }

    /**
     * Financial projection report.
     */
    public function financialProjection(Request $request)
    {
        $months = $request->input('months', 6); // Proyeksi untuk 6 bulan ke depan secara default
        $growthRate = $request->input('growth_rate', 5); // Asumsi pertumbuhan 5% per bulan
        $costIncreaseRate = $request->input('cost_increase_rate', 3); // Asumsi kenaikan biaya 3% per bulan

        // Dapatkan data keuangan 3 bulan terakhir sebagai dasar proyeksi
        $startDate = Carbon::now()->subMonths(3)->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        // Pendapatan per bulan selama 3 bulan terakhir
        $monthlyRevenue = Transaction::where('status', 'completed')
            ->where('created_at', '>=', $startDate)
            ->where('created_at', '<=', $endDate)
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as revenue')
            )
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // Jika data kurang dari 3 bulan, gunakan yang tersedia atau nilai default
        if ($monthlyRevenue->count() < 3) {
            // Gunakan data yang tersedia, atau buat nilai default
            if ($monthlyRevenue->count() == 0) {
                $averageMonthlyRevenue = 10000000; // Default 10 juta jika tidak ada data
            } else {
                $averageMonthlyRevenue = $monthlyRevenue->avg('revenue');
            }
        } else {
            // Gunakan rata-rata tertimbang: bulan terbaru memiliki bobot lebih tinggi
            $totalWeight = 0;
            $weightedSum = 0;

            for ($i = 0; $i < $monthlyRevenue->count(); $i++) {
                $weight = $i + 1; // Semakin baru, semakin tinggi bobotnya
                $weightedSum += $monthlyRevenue[$i]->revenue * $weight;
                $totalWeight += $weight;
            }

            $averageMonthlyRevenue = $weightedSum / $totalWeight;
        }

        // Data COGS dan biaya operasional dari bulan terakhir
        $lastMonthData = Transaction::where('status', 'completed')
            ->whereYear('created_at', Carbon::now()->year)
            ->whereMonth('created_at', Carbon::now()->month)
            ->sum('total_amount');

        if ($lastMonthData == 0) {
            $lastMonthData = $averageMonthlyRevenue; // Gunakan rata-rata jika tidak ada data bulan ini
        }

        $lastMonthCogs = $lastMonthData * 0.65; // Estimasi COGS 65% dari pendapatan
        $lastMonthOperating = $lastMonthData * 0.23; // Estimasi biaya operasional 23% dari pendapatan

        // Generate proyeksi
        $projections = [];
        $currentDate = Carbon::now();

        $projectedRevenue = $averageMonthlyRevenue;
        $projectedCogs = $lastMonthCogs;
        $projectedOperating = $lastMonthOperating;

        for ($i = 1; $i <= $months; $i++) {
            $currentDate = $currentDate->addMonth();

            // Tingkatkan proyeksi pendapatan dan biaya berdasarkan tingkat pertumbuhan
            $projectedRevenue *= (1 + $growthRate / 100);
            $projectedCogs *= (1 + $costIncreaseRate / 100);
            $projectedOperating *= (1 + $costIncreaseRate / 100);

            // Hitung profit
            $projectedGrossProfit = $projectedRevenue - $projectedCogs;
            $projectedNetProfit = $projectedGrossProfit - $projectedOperating;

            $projections[] = [
                'month' => $currentDate->format('M Y'),
                'revenue' => round($projectedRevenue),
                'cogs' => round($projectedCogs),
                'gross_profit' => round($projectedGrossProfit),
                'operating_expense' => round($projectedOperating),
                'net_profit' => round($projectedNetProfit),
                'profit_margin' => round(($projectedNetProfit / $projectedRevenue) * 100, 1)
            ];
        }

        // Persiapkan data untuk chart
        $projectionChartData = [
            'labels' => collect($projections)->pluck('month')->toArray(),
            'revenue' => collect($projections)->pluck('revenue')->toArray(),
            'expense' => collect($projections)->map(function($item) {
                return $item['cogs'] + $item['operating_expense'];
            })->toArray(),
            'profit' => collect($projections)->pluck('net_profit')->toArray()
        ];

        // Hitung total dan rata-rata untuk ringkasan
        $summary = [
            'total_projected_revenue' => collect($projections)->sum('revenue'),
            'total_projected_profit' => collect($projections)->sum('net_profit'),
            'average_monthly_revenue' => collect($projections)->avg('revenue'),
            'average_monthly_profit' => collect($projections)->avg('net_profit'),
            'average_profit_margin' => collect($projections)->avg('profit_margin')
        ];

        // Tambahkan skenario optimis dan pesimis (±20% dari proyeksi normal)
        $scenarios = [
            'optimistic' => [
                'revenue_growth' => $growthRate + 2,
                'cost_increase' => $costIncreaseRate - 1,
                'total_revenue' => $summary['total_projected_revenue'] * 1.2,
                'total_profit' => $summary['total_projected_profit'] * 1.3,
            ],
            'normal' => [
                'revenue_growth' => $growthRate,
                'cost_increase' => $costIncreaseRate,
                'total_revenue' => $summary['total_projected_revenue'],
                'total_profit' => $summary['total_projected_profit'],
            ],
            'pessimistic' => [
                'revenue_growth' => $growthRate - 2,
                'cost_increase' => $costIncreaseRate + 1,
                'total_revenue' => $summary['total_projected_revenue'] * 0.8,
                'total_profit' => $summary['total_projected_profit'] * 0.7,
            ]
        ];

        return view('reports.financial-projection', compact(
            'projections',
            'months',
            'growthRate',
            'costIncreaseRate',
            'projectionChartData',
            'summary',
            'scenarios'
        ));
    }
}
