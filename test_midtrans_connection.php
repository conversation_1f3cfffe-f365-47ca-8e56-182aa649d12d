<?php

require_once 'vendor/autoload.php';

use App\Models\Transaction;
use App\Models\TransactionItem;
use App\Models\ProcessedInventory;
use App\Services\MidtransService;
use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "🧪 TESTING MIDTRANS CONNECTION\n";
echo "================================\n\n";

// Test 1: Check Configuration
echo "1. Checking Midtrans Configuration...\n";
$serverKey = config('midtrans.server_key');
$clientKey = config('midtrans.client_key');
$merchantId = config('midtrans.merchant_id');
$isProduction = config('midtrans.is_production');

echo "   Server Key: " . substr($serverKey, 0, 10) . "...\n";
echo "   Client Key: " . substr($clientKey, 0, 10) . "...\n";
echo "   Merchant ID: {$merchantId}\n";
echo "   Environment: " . ($isProduction ? 'Production' : 'Sandbox') . "\n";

if (empty($serverKey) || empty($clientKey) || empty($merchantId)) {
    echo "   ❌ Missing Midtrans configuration!\n";
    exit(1);
}
echo "   ✅ Configuration OK\n\n";

// Test 2: Test Network Connection
echo "2. Testing Network Connection to Midtrans...\n";
$apiUrl = $isProduction ? 'https://api.midtrans.com' : 'https://api.sandbox.midtrans.com';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl . '/v2/ping');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Authorization: Basic ' . base64_encode($serverKey . ':'),
    'Content-Type: application/json'
]);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($endTime - $startTime) * 1000, 2);

if ($error) {
    echo "   ❌ Network Error: {$error}\n";
    echo "   Duration: {$duration}ms\n\n";
} else {
    echo "   ✅ Network connection OK\n";
    echo "   HTTP Code: {$httpCode}\n";
    echo "   Duration: {$duration}ms\n";
    echo "   Response: " . substr($response, 0, 100) . "...\n\n";
}

// Test 3: Test Snap Token Creation
echo "3. Testing Snap Token Creation...\n";

try {
    // Create test transaction
    $transaction = new Transaction();
    $transaction->id = 99999; // Test ID
    $transaction->total_amount = 50000;
    $transaction->customer_name = 'Test Customer';
    $transaction->customer_phone = '081234567890';
    
    // Create test item
    $item = new TransactionItem();
    $item->product_name = 'Test Ubi Bakar';
    $item->price = 25000;
    $item->quantity = 2;
    $item->subtotal = 50000;
    
    $transaction->setRelation('items', collect([$item]));
    
    $midtransService = new MidtransService();
    
    echo "   Creating snap token...\n";
    $startTime = microtime(true);
    $result = $midtransService->createSnapToken($transaction);
    $endTime = microtime(true);
    $duration = round(($endTime - $startTime) * 1000, 2);
    
    if ($result['success']) {
        echo "   ✅ Snap token created successfully!\n";
        echo "   Duration: {$duration}ms\n";
        echo "   Token: " . substr($result['snap_token'], 0, 30) . "...\n";
        echo "   Redirect URL: {$result['redirect_url']}\n\n";
    } else {
        echo "   ❌ Failed to create snap token\n";
        echo "   Duration: {$duration}ms\n";
        echo "   Error: {$result['message']}\n";
        if (isset($result['error_detail'])) {
            echo "   Detail: {$result['error_detail']}\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Exception: " . $e->getMessage() . "\n\n";
}

// Test 4: Test API Endpoint Performance
echo "4. Testing API Endpoint Performance...\n";

$testUrl = "http://localhost/ubi-bakar-cilembu/public/payment/create/1";
echo "   Testing URL: {$testUrl}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $testUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json',
    'X-Requested-With: XMLHttpRequest'
]);

$startTime = microtime(true);
$response = curl_exec($ch);
$endTime = microtime(true);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

$duration = round(($endTime - $startTime) * 1000, 2);

if ($error) {
    echo "   ❌ API Error: {$error}\n";
} else {
    echo "   ✅ API endpoint accessible\n";
    echo "   HTTP Code: {$httpCode}\n";
    echo "   Duration: {$duration}ms\n";
    
    if ($duration > 10000) {
        echo "   ⚠️  WARNING: Response time is very slow ({$duration}ms)\n";
    } elseif ($duration > 5000) {
        echo "   ⚠️  WARNING: Response time is slow ({$duration}ms)\n";
    } else {
        echo "   ✅ Response time is good ({$duration}ms)\n";
    }
    
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "   Response: " . json_encode($responseData, JSON_PRETTY_PRINT) . "\n";
    } else {
        echo "   Response: " . substr($response, 0, 200) . "...\n";
    }
}

echo "\n";

// Test 5: Performance Recommendations
echo "5. Performance Recommendations...\n";

if ($duration > 10000) {
    echo "   🔧 Very slow response detected. Recommendations:\n";
    echo "      - Check internet connection speed\n";
    echo "      - Verify Midtrans server status\n";
    echo "      - Consider implementing caching\n";
    echo "      - Add retry mechanism\n";
} elseif ($duration > 5000) {
    echo "   🔧 Slow response detected. Recommendations:\n";
    echo "      - Monitor network latency\n";
    echo "      - Add loading indicators\n";
    echo "      - Implement timeout handling\n";
} else {
    echo "   ✅ Performance is acceptable\n";
}

echo "\n🏁 Test completed!\n";
