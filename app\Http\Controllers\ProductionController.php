<?php

namespace App\Http\Controllers;

use App\Models\ProcessedInventory;
use App\Models\ProductionProcess;
use App\Models\RawInventory;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ProductionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $productions = ProductionProcess::with(['rawInventory', 'user'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('production.index', compact('productions'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $rawInventories = RawInventory::where('current_stock', '>', 0)
            ->where('is_active', true)
            ->get();
            
        return view('production.create', compact('rawInventories'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'raw_inventory_id' => 'required|exists:raw_inventory,id',
            'raw_quantity_used' => 'required|numeric|min:0.1',
            'processed_quantity_produced' => 'required|integer|min:1',
            'production_cost' => 'required|numeric|min:0',
            'product_type' => 'required|string',
            'production_date' => 'required|date',
            'expiry_date' => 'required|date|after:production_date',
            'selling_price' => 'required|numeric|min:0',
            'notes' => 'nullable|string'
        ]);
        
        DB::beginTransaction();
        
        try {
            // Get raw inventory
            $rawInventory = RawInventory::findOrFail($validated['raw_inventory_id']);
            
            // Check if enough stock
            if ($rawInventory->current_stock < $validated['raw_quantity_used']) {
                return back()->withErrors(['raw_quantity_used' => 'Stok ubi mentah tidak mencukupi'])
                    ->withInput();
            }
            
            // Create production process record
            $productionProcess = ProductionProcess::create([
                'batch_number' => ProductionProcess::generateBatchNumber(),
                'user_id' => Auth::id(),
                'raw_inventory_id' => $validated['raw_inventory_id'],
                'raw_quantity_used' => $validated['raw_quantity_used'],
                'processed_quantity_produced' => $validated['processed_quantity_produced'],
                'production_cost' => $validated['production_cost'],
                'production_date' => $validated['production_date'],
                'status' => 'completed',
                'notes' => $validated['notes']
            ]);
            
            // Update raw inventory stock
            $rawInventory->current_stock -= $validated['raw_quantity_used'];
            $rawInventory->save();
            
            // Calculate cost per item
            $rawMaterialCost = $rawInventory->cost_per_kg * $validated['raw_quantity_used'];
            $totalCost = $validated['production_cost'] + $rawMaterialCost;
            $costPerItem = $totalCost / $validated['processed_quantity_produced'];
            
            // Create processed inventory
            ProcessedInventory::create([
                'batch_number' => 'PROC-' . date('YmdHis'),
                'raw_inventory_id' => $validated['raw_inventory_id'],
                'quantity_processed_kg' => $validated['raw_quantity_used'],
                'quantity_produced' => $validated['processed_quantity_produced'],
                'cost_per_unit' => $costPerItem,
                'selling_price' => $validated['selling_price'],
                'production_date' => $validated['production_date'],
                'expiry_date' => $validated['expiry_date'],
                'product_type' => $validated['product_type'],
                'current_stock' => $validated['processed_quantity_produced'],
                'is_active' => true,
                'notes' => $validated['notes'],
                'min_stock_threshold' => 10, // Default value
                'name' => $validated['product_type'],
                'cost_per_item' => $costPerItem,
                'raw_material_per_item' => $validated['raw_quantity_used'] / $validated['processed_quantity_produced']
            ]);
            
            DB::commit();
            
            return redirect()->route('production.index')
                ->with('success', 'Proses produksi berhasil dicatat');
        } catch (\Exception $e) {
            DB::rollBack();
            return back()->withErrors(['error' => 'Terjadi kesalahan: ' . $e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(ProductionProcess $production)
    {
        $production->load(['rawInventory', 'user']);
        
        // Get processed inventory associated with this production
        $processedInventory = ProcessedInventory::where('raw_inventory_id', $production->raw_inventory_id)
            ->where('production_date', $production->production_date)
            ->first();
            
        return view('production.show', compact('production', 'processedInventory'));
    }

    /**
     * Generate production report
     */
    public function report(Request $request)
    {
        $startDate = $request->input('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('end_date', Carbon::now()->format('Y-m-d'));
        
        $productions = ProductionProcess::whereBetween('production_date', [$startDate, $endDate])
            ->with(['rawInventory', 'user'])
            ->orderBy('production_date', 'desc')
            ->get();
            
        $totalRawUsed = $productions->sum('raw_quantity_used');
        $totalProduced = $productions->sum('processed_quantity_produced');
        $totalCost = $productions->sum('production_cost');
        
        // Get daily production data for chart
        $dailyData = ProductionProcess::whereBetween('production_date', [$startDate, $endDate])
            ->select(DB::raw('DATE(production_date) as date'), DB::raw('SUM(processed_quantity_produced) as total'))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
            
        $labels = $dailyData->pluck('date')->toArray();
        $data = $dailyData->pluck('total')->toArray();
        
        return view('production.report', compact(
            'productions', 
            'startDate', 
            'endDate', 
            'totalRawUsed',
            'totalProduced',
            'totalCost',
            'labels',
            'data'
        ));
    }
}
