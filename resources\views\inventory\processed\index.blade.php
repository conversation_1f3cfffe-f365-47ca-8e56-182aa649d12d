@extends('layouts.app')

@section('content')
<div class="container-fluid">
    <div class="page-title d-flex justify-content-between align-items-center mb-4">
        <div class="d-flex align-items-center">
            <i class="fas fa-fire-alt me-2"></i>
            <span class="fw-bold">Produk Ubi <PERSON>ng</span>
        </div>
        <div class="button-group d-flex">
            <a href="{{ route('processed-inventory.create') }}" class="btn btn-primary me-2">
                <i class="fas fa-plus me-1"></i> Tambah Produk
            </a>
            <a href="{{ route('processed-inventory.show-process-form') }}" class="btn btn-success me-2">
                <i class="fas fa-industry me-1"></i> Proses Produksi
            </a>
            <a href="{{ route('production.reports') }}" class="btn btn-info">
                <i class="fas fa-chart-line me-1"></i> <PERSON><PERSON><PERSON>
            </a>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span class="fw-bold">Daftar Stok Ubi Matang</span>
                </div>
                <div class="card-body">
                    @if($lowStock->count() > 0)
                    <div class="alert alert-warning mb-4">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i> Peringatan Stok Menipis</h5>
                        <ul class="mb-0">
                            @foreach($lowStock as $item)
                            <li>{{ $item->name }} - tersisa {{ $item->current_stock }} pcs (minimum: {{ $item->min_stock_threshold ?? 0 }} pcs)</li>
                            @endforeach
                        </ul>
                    </div>
                    @endif

                    <div class="table-responsive">
                        <table class="custom-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Nama Produk</th>
                                    <th>Stok Saat Ini</th>
                                    <th>Harga Jual</th>
                                    <th>Biaya per Item</th>
                                    <th>Margin</th>
                                    <th>Bahan Baku</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($processedInventory as $index => $item)
                                <tr>
                                    <td>{{ $index + 1 }}</td>
                                    <td>{{ $item->name }}</td>
                                    <td>{{ $item->current_stock }} pcs</td>
                                    <td>Rp {{ number_format($item->selling_price, 0, ',', '.') }}</td>
                                    <td>
                                        @if($item->cost_per_item)
                                        Rp {{ number_format($item->cost_per_item, 0, ',', '.') }}
                                        @else
                                        -
                                        @endif
                                    </td>
                                    <td>
                                        @if($item->cost_per_item && $item->selling_price > 0)
                                        {{ number_format((($item->selling_price - $item->cost_per_item) / $item->selling_price) * 100, 1) }}%
                                        @else
                                        -
                                        @endif
                                    </td>
                                    <td>{{ $item->rawMaterial->name ?? '-' }}</td>
                                    <td>
                                        @if($item->current_stock <= ($item->min_stock_threshold ?? 0))
                                        <span class="status-badge danger">Stok Menipis</span>
                                        @else
                                        <span class="status-badge success">Tersedia</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ route('processed-inventory.edit', $item) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{{ route('processed-inventory.show', $item) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <form action="{{ route('processed-inventory.destroy', $item) }}" method="POST" class="d-inline" onsubmit="return confirm('Yakin ingin menghapus data ini?');">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="btn btn-sm btn-danger">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center">Tidak ada data produk ubi matang</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('styles')
<style>
    .page-title {
        background-color: #fff;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .page-title .fw-bold {
        font-size: 1.25rem;
    }
    
    .button-group .btn {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px 15px;
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    
    .button-group .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .btn-primary {
        background-color: #8B4513;
        border-color: #8B4513;
    }
    
    .btn-success {
        background-color: #4CAF50;
        border-color: #4CAF50;
    }
    
    .btn-info {
        background-color: #2196F3;
        border-color: #2196F3;
        color: white;
    }
    
    .btn-info:hover {
        color: white;
    }
    
    @media (max-width: 768px) {
        .page-title {
            flex-direction: column;
            align-items: flex-start !important;
        }
        
        .button-group {
            margin-top: 15px;
            width: 100%;
        }
        
        .button-group .btn {
            flex: 1;
            margin-bottom: 5px;
        }
    }
</style>
@endpush
@endsection 